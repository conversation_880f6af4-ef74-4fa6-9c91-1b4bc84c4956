package com.adins.esign.businesslogic.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrJobCheckRegisterStatus;
import com.adins.esign.model.TrManualReport;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.exceptions.RemoteException;
import com.adins.framework.tool.lang.FormatterUtils;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.utils.IOUtils;
import com.aliyun.oss.internal.OSSUtils;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.DeleteObjectsResult;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.google.common.base.Stopwatch;

@Component
public class AliyunOssCloudStorageLogic implements CloudStorageLogic {
	@Autowired private OSS ossClient;
	@Autowired private PersonalDataEncryptionLogic encryptionLogic; 
	@Value("${esignhub.cloudstorage.bucket}") private String bucketName;
	private static final Logger LOG = LoggerFactory.getLogger(AliyunOssCloudStorageLogic.class);

	@Override
	public String storeRegistrationSelfie(String nik, byte[] photo) {
		String fileName = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.PERSONAL_SELFIE_FILENAME_FORMAT, nik);
		byte[] encryptBytearr = encryptionLogic.encrypt(photo);
		this.uploadToOss(fileName, encryptBytearr);
		return fileName;
	}

	@Override
	public String storeRegistrationKtp(String nik, byte[] photo) {		
		String fileName =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.PERSONAL_KTP_FILENAME_FORMAT, nik);
		byte[] cipheredBytes = encryptionLogic.encrypt(photo);
		this.uploadToOss(fileName, cipheredBytes);
		return fileName;
	}
	
	private void uploadToOss(String fileName, byte[] bytearr) {
		LOG.info("Storing {} to OSS", fileName);
		Stopwatch sw = Stopwatch.createStarted();
		ossClient.putObject(bucketName, fileName, new ByteArrayInputStream(bytearr));
		sw.stop();
		LOG.info("Upload oss {}bytes = {}ms", bytearr.length, sw.elapsed(TimeUnit.MILLISECONDS));
	}

	@Override
	public byte[] getContentKtp(String key) {
		byte[] cipheredBytes = this.downloadFromOss(key);
		if (null == cipheredBytes) {
			return null;
		}
		return encryptionLogic.decrypt(cipheredBytes);
	}

	@Override
	public byte[] getContentNonKtp(String key) {
		return this.downloadFromOss(key);
	}
	
	private byte[] downloadFromOss(String fileName) {
		if (!OSSUtils.validateObjectKey(fileName)) {
			return null;
		}
		
		LOG.info("Getting {} from OSS", fileName);
		Stopwatch sw = Stopwatch.createStarted();
		try {
			OSSObject object = ossClient.getObject(bucketName, fileName);		
			sw.stop();		
			InputStream is = object.getObjectContent();
			
			byte[] bytearr = IOUtils.readStreamAsByteArray(is);
			LOG.info("Download oss {}bytes = {}ms", bytearr.length, sw.elapsed(TimeUnit.MILLISECONDS));
			return bytearr;
		}
		catch (IOException e) {
			throw new RemoteException("Fail download object from OSS, key=" + fileName);
		}
		catch (OSSException osse) {
			LOG.warn("Exception on downloading {} from OSS: {}", fileName, osse.getLocalizedMessage());
			return null;
		}
	}

	@Override
	public String storeTransactionSelfie(String tenantCode, String nik, String refNumber, byte[] photo) {
		String dateStr = FormatterUtils.formatDate(new Date(), GlobalVal.DATE_FORMAT);
		
		String fileName =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.TRX_SELFIE_FILENAME_FORMAT,
				tenantCode, nik, dateStr, refNumber, System.currentTimeMillis());
		byte[] encryptedPhoto = encryptionLogic.encrypt(photo);
		this.uploadToOss(fileName, encryptedPhoto);
		return fileName;
	}
	
	private void deleteFromOss(String fileName) {
		Stopwatch sw = Stopwatch.createStarted();
		String nextMarker = null;
		fileName =  GlobalVal.OSS_PREFIX_ENCRYPTED + fileName; 
		ObjectListing objectListing = null;
		LOG.info("Deleting {} from OSS", fileName);
		do {
			ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName)
	                .withPrefix(fileName)
	                .withMarker(nextMarker);		
			objectListing = ossClient.listObjects(listObjectsRequest);
			
			if(!objectListing.getObjectSummaries().isEmpty()) {
				List<String> keys = new ArrayList<>();
                for (OSSObjectSummary s : objectListing.getObjectSummaries()) {
                    LOG.info("Key name: {}", s.getKey());
                    keys.add(s.getKey());
                }
				DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(bucketName).withKeys(keys);
				DeleteObjectsResult  deleteObjectsResult = ossClient.deleteObjects(deleteObjectsRequest);
	            List<String> deletedObjects = deleteObjectsResult.getDeletedObjects();
	            for(String obj : deletedObjects) {
	               LOG.info("Deleted object: {}", obj);
	            }
			}
			nextMarker = objectListing.getNextMarker();
		}while (objectListing.isTruncated());
		sw.stop();
		LOG.info("Delete from oss {}ms", sw.elapsed(TimeUnit.MILLISECONDS));
	}
	
	@Override
	public String deleteFileFromOss(String refNumber) {		
		String fileName =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.DOCUMENT_PDFDOC_FORMAT, refNumber);
		this.deleteFromOss(fileName);
		return fileName;
	}

	@Override
	public String storeDocument(String refNumber, String documentId, byte[] document) {
		String filename =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.DOCUMENT_PDFDOC_DETAIL_FORMAT, refNumber, documentId);
		byte[] encryptBytearr = encryptionLogic.encrypt(document);
		this.uploadToOss(filename, encryptBytearr);
		return filename;
	}
	
	@Override
	public String storeDocumentUrlDummy(String tenantCode, String refNumber, byte[] document) {
		String filename =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.DOCUMENT_STAMPING_RESULT_DETAIL_FORMAT, tenantCode, refNumber);
		byte[] encryptBytearr = encryptionLogic.encrypt(document);
		this.uploadToOss(filename, encryptBytearr);
		return filename;
	}

	@Override
	public String storeStampingDocument(String tenantCode, String refNumber, String documentId, byte[] document) {
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.DOCUMENT_STAMPINGDOC_FORMAT, tenantCode, refNumber, documentId);
		byte[] encryptBytearr = encryptionLogic.encrypt(document);
		this.uploadToOss(filename, encryptBytearr);
		return filename;
	}

	@Override
	public void deleteStampingDocument(String tenantCode, String refNumber, String documentId) {
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.DOCUMENT_STAMPINGDOC_FORMAT, tenantCode, refNumber, documentId);
		deleteFromOss(filename);
	}
	
	@Override
	public byte[] getStampingDocument(String tenantCode, String refNumber, String documentId) {
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.DOCUMENT_STAMPINGDOC_FORMAT, tenantCode, refNumber, documentId);
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename));
		return decryptFile;
	}

	@Override
	public String storeStampedDocument(TrDocumentD document, byte[] documentByteArray) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.STAMPED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] encryptBytearr = encryptionLogic.encrypt(documentByteArray);
		uploadToOss(filename, encryptBytearr);
		return filename;
	}

	@Override
	public void deleteStampedDocument(TrDocumentD document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.STAMPED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		deleteFromOss(filename);
	}

	@Override
	public byte[] getStampedDocument(TrDocumentD document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.STAMPED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename));
		return decryptFile;
	}

	@Override
	public String storeStampingPaymentReceipt(TrDocumentD document, byte[] documentByteArray) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.PAYMENT_RECEIPT_STAMPING_FORMAT, year, month, document.getDocumentId());
		byte[] encryptBytearr = encryptionLogic.encrypt(documentByteArray);
		uploadToOss(filename, encryptBytearr);
		return filename;
	}

	@Override
	public void deleteStampingPaymentReceipt(TrDocumentD document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.PAYMENT_RECEIPT_STAMPING_FORMAT, year, month, document.getDocumentId());
		deleteFromOss(filename);
	}

	@Override
	public byte[] getStampingPaymentReceipt(TrDocumentD document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.PAYMENT_RECEIPT_STAMPING_FORMAT, year, month, document.getDocumentId());
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename));
		return decryptFile;
	}
	
	//GAPERLU
	@Override
	public String storeDocumentSignedTknaj(String year, String month, String refNumber, String documentId, byte[] document) {
		String filename = String.format(GlobalVal.DOCUMENT_SIGN_COMPLETE_FORMAT, year, month, refNumber, documentId);
		this.uploadToOss(filename, document);
		return filename;
	}
	
	@Override
	public byte[] getDocumentTknAj(TrDocumentH docH,TrDocumentD document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getCompletedDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = String.format(GlobalVal.DOCUMENT_SIGN_COMPLETE_FORMAT, year, month, docH.getRefNumber() ,document.getDocumentId());
		return downloadFromOss(filename);
	}

	//GAPERLU

	@Override
	public String storeBaseSignDocument(TrDocumentD document, byte[] documentByteArray) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.BASE_SIGN_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] encryptBytearr = encryptionLogic.encrypt(documentByteArray);

		uploadToOss(filename, encryptBytearr);
		return filename;
	}

	@Override
	public void deleteBaseSignDocument(TrDocumentD document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.BASE_SIGN_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		deleteFromOss(filename);
	}

	@Override
	public byte[] getBaseSignDocument(TrDocumentD document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.BASE_SIGN_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename));

		return decryptFile;
	}

	@Override
	public String storeSignedDocument(TrDocumentD document, byte[] documentByteArray) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.SIGNED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] encryptBytearr = encryptionLogic.encrypt(documentByteArray);

		uploadToOss(filename, encryptBytearr);
		return filename;
	}

	@Override
	public void deleteSignedDocument(TrDocumentD document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.SIGNED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		deleteFromOss(filename);
	}

	@Override
	public byte[] getSignedDocument(TrDocumentD document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getRequestDate());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.SIGNED_DOCUMENT_FORMAT, year, month, document.getDocumentId());
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename));
		return decryptFile;
	}

	@Override
	public byte[] getManualReport(TrManualReport document) {
		
		String filename =  String.format(GlobalVal.DOWNLOAD_MANUAL_REPORT,document.getMsTenant().getTenantCode(), document.getFileName());
		
		return downloadFromOss(filename);
	}

	@Override
	public String storeUserSelfie(String trxNo, Date trxDate, byte[] selfiePhoto) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(trxDate);
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename =  GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.USER_SELFIE_FORMAT, year, month, trxNo);
		byte[] encryptBytearr = encryptionLogic.encrypt(selfiePhoto);

		uploadToOss(filename, encryptBytearr);
		return filename;
	}

	@Override
	public String storeRegisterRequest(TrJobCheckRegisterStatus jobCheckRegisterStatus, String jsonRequest) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(jobCheckRegisterStatus.getDtmCrt());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String filename = String.format(GlobalVal.REGISTER_REQUEST_FORMAT, year, month, jobCheckRegisterStatus.getIdJobCheckRegisterStatus());

		uploadToOss(filename, jsonRequest.getBytes());
		return filename;
	}

	@Override
	public String storeSaveManualStamp(TrDocumentD docD, byte[] document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(docD.getDtmCrt());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.MANUAL_STAMP_FORMAT, year, month, docD.getDocumentId());
		byte[] encryptBytearr = encryptionLogic.encrypt(document);

		this.uploadToOss(filename, encryptBytearr);
		return filename;
	}

	@Override
	public byte[] getManualStamp(TrDocumentD document) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(document.getDtmCrt());
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		String filename = GlobalVal.OSS_PREFIX_ENCRYPTED + String.format(GlobalVal.MANUAL_STAMP_FORMAT, year, month, document.getDocumentId());
		byte[] decryptFile = encryptionLogic.decrypt(downloadFromOss(filename));

		return decryptFile;
	}

	//TIDAK PERLU
	@Override
	public String storeCertificateTknAj(String tekenId, byte[] certificate) {

		String fileName = String.format(GlobalVal.DOWNLOAD_CERTIFICATE_FORMAT, tekenId);
		this.uploadToOss(fileName, certificate);
		return fileName;
	}

	@Override
	public byte[] getCertificateTknAj(String tekenId) {
		String filename = String.format(GlobalVal.DOWNLOAD_CERTIFICATE_FORMAT,tekenId);
		
		return downloadFromOss(filename);
	}
	//TIDAK PERLU
	
	@Override
	public String storeAutosignExcel(Date trxDate, String tenantCode, String vendorCode, String filename, long idProcessExcelBmH, byte[] base64Excel) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(trxDate);
		
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		
		String fileName = String.format(GlobalVal.AUTOSIGN_BM_EXCEL_UPLOAD, year, month, tenantCode, idProcessExcelBmH, filename);

		this.uploadToOss(fileName, base64Excel);
		return fileName;
	}

	@Override
	public String storeManualReportUpload(String tenantCode,  String filename, byte[] base64Excel) {

		String fileName = String.format(GlobalVal.REPORT_MANUAL_UPLOAD,  tenantCode, filename);

		this.uploadToOss(fileName, base64Excel);
		return fileName;
	}
	
	@Override
	public String deleteManualReport(String tenantCode,  String filename) {

		String fileName = String.format(GlobalVal.REPORT_MANUAL_UPLOAD,  tenantCode, filename);
		this.deleteFromOss(fileName);
		return fileName;
	}
	
	@Override
	public byte[] getTemplateExcelAutosignBm() {
		return downloadFromOss(GlobalVal.TEMPLATE_EXCEL_AUTOSIGN_BM);
	}

	@Override
	public String storeLivenessFaceComparePhoto(long idTrSigningProcessAuditLog, byte[] selfiePhoto) {
		
		String filename = String.format(GlobalVal.AUDIT_TRAIL_SELFIE_PHOTO, idTrSigningProcessAuditLog);

		this.uploadToOss(filename, selfiePhoto);
		
		return filename;
	}

	@Override
	public String storeZippedApiLogAuditTrail(String subfolderName, TrSigningProcessAuditTrail trail, byte[] zippedTextFile) {
		String filename = String.format(GlobalVal.AUDIT_TRAIL_API_LOG_FORMAT, subfolderName, trail.getIdSigningProcessAuditTrail());

		uploadToOss(filename, zippedTextFile);
		return filename;
	}

	@Override
	public byte[] getZippedApiLogAuditTrail(String subfolderName, TrSigningProcessAuditTrail trail) {
		String filename = String.format(GlobalVal.AUDIT_TRAIL_API_LOG_FORMAT, subfolderName, trail.getIdSigningProcessAuditTrail());

		return downloadFromOss(filename);
	}
	
	@Override
	public byte[] getCustomSign(String tenantCode, String idNo) {
		String filename = String.format(GlobalVal.POA_CUSTOM_SIGN, tenantCode, idNo);

		return downloadFromOss(filename);
	}
	
}
