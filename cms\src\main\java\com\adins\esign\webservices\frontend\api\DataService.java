package com.adins.esign.webservices.frontend.api;

import com.adins.esign.webservices.model.AddBalanceTypeRequest;
import com.adins.esign.webservices.model.AddBalanceTypeResponse;
import com.adins.esign.webservices.model.BusinessLineListRequest;
import com.adins.esign.webservices.model.BusinessLineListResponse;
import com.adins.esign.webservices.model.GetListTenantRequest;
import com.adins.esign.webservices.model.GetListTenantResponse;
import com.adins.esign.webservices.model.GetPeruriDocumentTypeRequest;
import com.adins.esign.webservices.model.GetProvinceByInvitationRequest;
import com.adins.esign.webservices.model.GetProvinceEmbedRequest;
import com.adins.esign.webservices.model.GetProvinceRequest;
import com.adins.esign.webservices.model.GetProvinceResponse;
import com.adins.esign.webservices.model.GetPsrePriorityRequest;
import com.adins.esign.webservices.model.GetPsrePriorityResponse;
import com.adins.esign.webservices.model.GetStatusStampingOtomatisTenantRequest;
import com.adins.esign.webservices.model.GetStatusStampingOtomatisTenantResponse;
import com.adins.esign.webservices.model.GetSubDistrictByInvitationRequest;
import com.adins.esign.webservices.model.GetSubDistrictEmbedRequest;
import com.adins.esign.webservices.model.GetSubDistrictRequest;
import com.adins.esign.webservices.model.GetSubDistrictResponse;
import com.adins.esign.webservices.model.GetDistrictByInvitationRequest;
import com.adins.esign.webservices.model.GetDistrictEmbedRequest;
import com.adins.esign.webservices.model.GetDistrictRequest;
import com.adins.esign.webservices.model.GetDistrictResponse;
import com.adins.esign.webservices.model.GetDocumentEMateraiTypeRequest;
import com.adins.esign.webservices.model.GetDocumentEMateraiTypeResponse;
import com.adins.esign.webservices.model.GetGeneralSettingRequest;
import com.adins.esign.webservices.model.GetGeneralSettingResponse;
import com.adins.esign.webservices.model.GetListPSrESettingRequest;
import com.adins.esign.webservices.model.GetListPSrESettingResponse;
import com.adins.esign.webservices.model.GetListPaymentTypeRequest;
import com.adins.esign.webservices.model.LovListRequest;
import com.adins.esign.webservices.model.LovListResponse;
import com.adins.esign.webservices.model.OfficeListEmbedRequest;
import com.adins.esign.webservices.model.OfficeListRequest;
import com.adins.esign.webservices.model.OfficeListResponse;
import com.adins.esign.webservices.model.PaymentSignTypeListRequest;
import com.adins.esign.webservices.model.PaymentSignTypeListResponse;
import com.adins.esign.webservices.model.RegionListEmbedRequest;
import com.adins.esign.webservices.model.RegionListRequest;
import com.adins.esign.webservices.model.RegionListResponse;
import com.adins.esign.webservices.model.VendorListEmbedRequest;
import com.adins.esign.webservices.model.VendorListInvitationRegisterRequest;
import com.adins.esign.webservices.model.VendorListRequest;
import com.adins.esign.webservices.model.VendorListResponse;
import com.adins.framework.service.base.model.MssRequestType;

public interface DataService {
	LovListResponse getLovList(LovListRequest request);
	LovListResponse getLovListEmbed(LovListRequest request);
	PaymentSignTypeListResponse getPaymentSignTypeList(PaymentSignTypeListRequest request);
	PaymentSignTypeListResponse getListPaymentType(GetListPaymentTypeRequest request);
	VendorListResponse getVendorList(VendorListRequest request);
	VendorListResponse getVendorUnsecureList(VendorListRequest request);
	VendorListResponse getVendorListEmbed(VendorListEmbedRequest request);
	VendorListResponse getVendorListInvReg(VendorListInvitationRegisterRequest request);
	GetListTenantResponse getTenantList(GetListTenantRequest request);
	BusinessLineListResponse getBusinessLineList(BusinessLineListRequest request);
	OfficeListResponse getOfficeList(OfficeListRequest request);
	OfficeListResponse getOfficeListEmbed(OfficeListEmbedRequest request);
	RegionListResponse getRegionList(RegionListRequest request);
	RegionListResponse getRegionListEmbed(RegionListEmbedRequest request);
	VendorListResponse getAllVendorList(MssRequestType request);
	AddBalanceTypeResponse addBalanceType(AddBalanceTypeRequest request);
	AddBalanceTypeResponse editBalanceType(AddBalanceTypeRequest request);
	GetSubDistrictResponse getSubDistrictList(GetSubDistrictRequest request);
	GetSubDistrictResponse getSubDistrictListEmbed(GetSubDistrictEmbedRequest request);
	GetSubDistrictResponse getSubDistrictListByInvitation(GetSubDistrictByInvitationRequest request);
	GetDistrictResponse getDistrictList(GetDistrictRequest request);
	GetDistrictResponse getDistrictListEmbed(GetDistrictEmbedRequest request);
	GetDistrictResponse getDistrictListByInvitation(GetDistrictByInvitationRequest request);
	GetProvinceResponse getProvinceList(GetProvinceRequest request);
	GetProvinceResponse getProvinceListEmbed(GetProvinceEmbedRequest request);
	GetProvinceResponse getProvinceListInvReg(GetProvinceByInvitationRequest request);
	GetDocumentEMateraiTypeResponse getListDocumentEMateraiTypeEmbed(GetDocumentEMateraiTypeRequest request);
	GetDocumentEMateraiTypeResponse getListPeruriDocumentType(GetPeruriDocumentTypeRequest request);
	GetStatusStampingOtomatisTenantResponse getStatusStampingOtomatisTenant(GetStatusStampingOtomatisTenantRequest request);
	VendorListResponse getVendorListV2(VendorListRequest request);
	GetListPSrESettingResponse getListPSrESetting(GetListPSrESettingRequest request);
	GetPsrePriorityResponse getPsrePriority(GetPsrePriorityRequest request);
	GetGeneralSettingResponse getGeneralSetting(GetGeneralSettingRequest request);
}