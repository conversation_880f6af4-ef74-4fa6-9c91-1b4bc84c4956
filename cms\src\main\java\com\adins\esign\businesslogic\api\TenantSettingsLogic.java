package com.adins.esign.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.model.MsTenant;
import com.adins.esign.webservices.model.ListTenantSettingsRequest;
import com.adins.esign.webservices.model.ListTenantSettingsResponse;
import com.adins.esign.webservices.model.SaveTenantSettingsRequest;
import com.adins.esign.webservices.model.SaveTenantSettingsResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface TenantSettingsLogic {
	/**
	 * @param tenant
	 * @param settingTypeCode
	 * @return <code>true</code> when ms_tenant_settings.setting_value = "1"
	 */
	boolean getSettingValue(MsTenant tenant, String settingTypeCode);
	
	int getSettingValue(MsTenant tenant, String settingTypeCode, int defaultValue);
	long getSettingValue(MsTenant tenant, String settingTypeCode, long defaultValue);
	short getSettingValue(MsTenant tenant, String settingTypeCode, short defaultValue);
	String getSettingValue(MsTenant tenant, String settingTypeCode, String defaultValue);
	
	@RolesAllowed({"ROLE_TENANT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ListTenantSettingsResponse getListSettingValue(ListTenantSettingsRequest request, AuditContext audit);

	@RolesAllowed({"ROLE_TENANT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
    SaveTenantSettingsResponse saveTenantSettings(SaveTenantSettingsRequest request, AuditContext audit);
}
