package com.adins.esign.validatorlogic.impl;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.enums.RegistrationType;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterByInvitationRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterRequest;
import com.adins.exceptions.FormatException;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.UserManagementException;
import com.adins.exceptions.FormatException.ReasonFormat;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.RegisterException.ReasonRegister;
import com.adins.exceptions.RegisterException;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.UserManagementException.ReasonUserManagement;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Component
public class GenericUserValidatorLogic extends BaseLogic implements UserValidatorLogic {

	private static final Logger LOG = LoggerFactory.getLogger(GenericUserValidatorLogic.class);
	
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncryptionLogic;
	
	private static final String PARAM_EMAIL = "Email";

	@Override
	public AmMsuser validateGetUserByEmail(String email, boolean checkUserExistence, AuditContext audit) {
		if (StringUtils.isBlank(email)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM, new String[] {PARAM_EMAIL}, audit),
					ReasonUser.EMAIL_EMPTY);
		}
		
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(email);
		if (checkUserExistence && null == user) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_EMAIL_NOT_FOUND, new String[] {email}, audit),
					ReasonUser.LOGIN_ID_NOT_EXISTS);
		}	
		return user;
	}
	
	@Override
	public void validateTekenAjaRegisterRequest(TekenAjaRegisterRequest request, RegistrationType registerType, AuditContext audit) {
		// Validasi email diisi / tidak
		if ((RegistrationType.EMBED == registerType || RegistrationType.NORMAL == registerType || RegistrationType.INVITATION_EMAIL == registerType)
				&& StringUtils.isBlank(request.getUserData().getEmail())) {
			throw new UserException(getMessage("businesslogic.document.emptyemail", null, audit), ReasonUser.EMAIL_EMPTY);
		}
		
		// Validasi tanggal lahir
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
			sdf.setLenient(false);
			sdf.parse(request.getUserData().getTglLahir());
		} catch (Exception e) {
			String message = e.getLocalizedMessage();
			if (StringUtils.contains(message, "\"")) {
				// " direplace ke ' supaya tidak ada \ di error message API
				message = message.replace("\"", "'");
			}
			throw new FormatException(message, ReasonFormat.INVALID_DATE_FORMAT);
		}
	}
	@Override
	public void validateGetUserDataCaller(String loginId, String tenantCode, AuditContext audit) {
		boolean isRoleValid = daoFactory.getRoleDao().isMenuAvailableForTenantUser(audit.getCallerId(), tenantCode, GlobalVal.MENU_INQ_USER);
		
		if (!audit.getCallerId().equalsIgnoreCase(loginId) && !isRoleValid) {
			LOG.debug("User {} from tenant {} cannot access {}'s data", audit.getCallerId(), tenantCode, loginId);
			throw new UserException(getMessage("businesslogic.global.invalidrole", null, audit), ReasonUser.INVALID_USER_ROLE);
		}
	}
	
	@Override
	public AmMsuser validateGetUserByEmailv2(String email, boolean checkUserExistence, AuditContext audit) {
		List<Map<String, Object>> idMsusers = daoFactory.getUserDao().getDistinctIdMsusersByEmail(email);
		
		// If id_ms_user not found
		if (CollectionUtils.isEmpty(idMsusers)) {
			if (checkUserExistence) {
				throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_EMAILNOTFOUND, new Object[] {email}, audit), ReasonUser.USER_NOT_FOUND);
			}
			return null;
		}
		
		// If there is more than 1 id_ms_user
		if (idMsusers.size() > 1) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_EMAILUSED_BY_MANY,new Object[] {email, idMsusers.size()}, audit),
					ReasonUser.LOGIN_ID_NOT_UNIQUE);
		}
		
		BigInteger idMsuser = (BigInteger) idMsusers.get(0).get("d0");
		return daoFactory.getUserDao().getUserByIdMsUser(idMsuser.longValue());
	}
	
	@Override
	public AmMsuser validateGetUserByPhone(String phone, boolean checkUserExistence, AuditContext audit) {
		List<Map<String, Object>> idMsusers = daoFactory.getUserDao().getDistinctIdMsusersByPhone(phone);
		
		// If id_ms_user not found
		if (CollectionUtils.isEmpty(idMsusers)) {
			if (checkUserExistence) {
				throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_PHONENOTFOUND,new Object[] {phone}, audit),
						ReasonUser.USER_NOT_FOUND);
			}
			return null;
		}
		
		// If there is more than 1 id_ms_user
		if (idMsusers.size() > 1) {
			throw new UserException(getMessage("businesslogic.userval.phoneusedbymany",new Object[] {phone, idMsusers.size()}, audit),
					ReasonUser.LOGIN_ID_NOT_UNIQUE);
		}
		
		BigInteger idMsuser = (BigInteger) idMsusers.get(0).get("d0");
		return daoFactory.getUserDao().getUserByIdMsUser(idMsuser.longValue());
	}
	@Override
	public AmMsuser validateGetUserByEmailAndVendorCode(String email, boolean checkUserExistence, String vendorCode,
			AuditContext audit) {
		List<Map<String, Object>> idMsusers = daoFactory.getUserDao().getDistinctIdMsusersByEmailAndVendor(email, vendorCode);
		
		// If id_ms_user not found
		if (CollectionUtils.isEmpty(idMsusers)) {
			if (checkUserExistence) {
				throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_EMAILNOTFOUND, new Object[] {email}, audit), ReasonUser.USER_NOT_FOUND);
			}
			return null;
		}
		
		// If there is more than 1 id_ms_user
		if (idMsusers.size() > 1) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_EMAILUSED_BY_MANY,new Object[] {email, idMsusers.size()}, audit),
					ReasonUser.LOGIN_ID_NOT_UNIQUE);
		}
		
		BigInteger idMsuser = (BigInteger) idMsusers.get(0).get("d0");
		return daoFactory.getUserDao().getUserByIdMsUser(idMsuser.longValue());
	}
	@Override
	public AmMsuser validateGetUserByPhoneAndVendorCode(String phone, boolean checkUserExistence, String vendorCode,
			AuditContext audit) {
		List<Map<String, Object>> idMsusers = daoFactory.getUserDao().getDistinctIdMsusersByPhoneAndVendor(phone, vendorCode);
		
		// If id_ms_user not found
		if (CollectionUtils.isEmpty(idMsusers)) {
			if (checkUserExistence) {
				throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_PHONENOTFOUND,new Object[] {phone}, audit),
						ReasonUser.USER_NOT_FOUND);
			}
			return null;
		}
		
		// If there is more than 1 id_ms_user
		if (idMsusers.size() > 1) {
			throw new UserException(getMessage("businesslogic.userval.phoneusedbymany",new Object[] {phone, idMsusers.size()}, audit),
					ReasonUser.LOGIN_ID_NOT_UNIQUE);
		}
		
		BigInteger idMsuser = (BigInteger) idMsusers.get(0).get("d0");
		return daoFactory.getUserDao().getUserByIdMsUser(idMsuser.longValue());
	}
	
	@Override
	public AmMsuser validateGetUserByNik(String nik, boolean checkUserExistence, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getUserByIdNo(nik);
		if (checkUserExistence && null == user) {
			throw new UserException(getMessage("businesslogic.userval.niknotfound", new Object[] {nik}, audit), ReasonUser.USER_NOT_FOUND);
		}
		return user;
	}
	
	@Override
	public void validateTekenAjaRegisterByInvRequest(TekenAjaRegisterByInvitationRequest request, RegistrationType registrationType, AuditContext audit) {
		if (RegistrationType.INVITATION_EMAIL == registrationType && StringUtils.isBlank(request.getUserData().getEmail())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {PARAM_EMAIL}, audit), ReasonUser.PARAM_INVALID);
		}
	}
	
	@Override
	public void validateRegisterUserBean(UserBean userData, AuditContext audit) {
		String idPhoto = MssTool.cutImageStringPrefix(userData.getIdPhoto());
		if (StringUtils.isEmpty(idPhoto)) {
			throw new ParameterException(getMessage("businesslogic.global.mandatoryphoto", new String[] {"Foto KTP"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		String selfiePhoto = MssTool.cutImageStringPrefix(userData.getSelfPhoto());
		if (StringUtils.isEmpty(selfiePhoto)) {
			throw new ParameterException(getMessage("businesslogic.global.mandatoryphoto", new String[] {"Foto Diri"}, audit), ReasonParam.MANDATORY_PARAM);
		}
	}
	
	@Override
	public boolean usePsreMandatoryParamOnly(MsTenant tenant) {
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "PSRE_MANDATORY_REGIS_PARAM_FLAG");
		if (null == tenantSettings) {
			return false;
		}
		
		return "1".equals(tenantSettings.getSettingValue());
	}
	
	private void validateAllPsreRegistrationParam(RegisterExternalRequest request, MsTenant tenant, AuditContext audit) {
		validateFullname(request.getNama(), audit);
		validateEmail(request.getEmail(), tenant, audit);
		validatePlaceOfBirth(request.getTmpLahir(), audit);
		validateDateOfBirth(request.getTglLahir(), audit);
		validateGender(request.getJenisKelamin(), audit);
		validatePhoneNumber(request.getTlp(), audit);
		validateIdNo(request.getIdKtp(), audit);
		validateAddress(request.getAlamat(), audit);
		validateKecamatan(request.getKecamatan(), audit);
		validateKelurahan(request.getKelurahan(), audit);
		validateKota(request.getKota(), audit);
		validateProvinsi(request.getProvinsi(), audit);
		validateKodePos(request.getKodePos(), audit);
		validateSelfPhoto(request.getSelfPhoto(), audit);
		validateIdPhoto(request.getIdPhoto(), audit);
	}
	
	private void validatePsreMandatoryParam(RegisterExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		
		if (GlobalVal.VENDOR_CODE_VIDA.equals(vendor.getVendorCode())) {
			
			validateFullname(request.getNama(), audit);
			validateIdNo(request.getIdKtp(), audit);
			validateDateOfBirth(request.getTglLahir(), audit);
			validatePhoneNumber(request.getTlp(), audit);
			validateEmail(request.getEmail(), tenant, audit);
			validateSelfPhoto(request.getSelfPhoto(), audit);
			
			request.setTmpLahir(null);
			request.setJenisKelamin(null);
			request.setAlamat(null);
			request.setKecamatan(null);
			request.setKelurahan(null);
			request.setKota(null);
			request.setProvinsi(null);
			request.setKodePos(null);
			request.setIdPhoto(null);
			
		}
		
		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
			
			validateFullname(request.getNama(), audit);
			validateIdNo(request.getIdKtp(), audit);
			validateDateOfBirth(request.getTglLahir(), audit);
			validatePhoneNumber(request.getTlp(), audit);
			validateEmail(request.getEmail(), tenant, audit);
			validateSelfPhoto(request.getSelfPhoto(), audit);
			validateIdPhoto(request.getIdPhoto(), audit);
			
			request.setTmpLahir(null);
			request.setJenisKelamin(null);
			request.setAlamat(null);
			request.setKecamatan(null);
			request.setKelurahan(null);
			request.setKota(null);
			request.setProvinsi(null);
			request.setKodePos(null);
			
		}
		
		if (GlobalVal.VENDOR_CODE_DIGISIGN.equals(vendor.getVendorCode())) {
			validateAllPsreRegistrationParam(request, tenant, audit);
		}
	}
	
	@Override
	public void validateExternalRegisterRequestParam(RegisterExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		
		validatePassword(request.getPassword(), tenant, audit);
		
		if (usePsreMandatoryParamOnly(tenant)) {
			validatePsreMandatoryParam(request, tenant, vendor, audit);
			return;
		}
		
		validateAllPsreRegistrationParam(request, tenant, audit);
		
	}
	
	@Override
	public AmMsuser validateGetUserByPhoneAndEmail(String phone, String email, boolean checkUserExistence,
			AuditContext audit) {
		List<Map<String, Object>> idMsusers = daoFactory.getUserDao().getDistinctIdMsusersByPhoneAndEmail(phone, email);
		
		// If id_ms_user not found
		if (CollectionUtils.isEmpty(idMsusers)) {
			if (checkUserExistence) {
				throw new UserException(getMessage("businesslogic.userval.phoneandemailnotfound",new Object[] {phone, email}, audit),
						ReasonUser.USER_NOT_FOUND);
			}
			return null;
		}
				
		// If there is more than 1 id_ms_user
		if (idMsusers.size() > 1) {
			throw new UserException(getMessage("businesslogic.userval.phoneandemailusedbymany",new Object[] {phone, email, idMsusers.size()}, audit),
					ReasonUser.LOGIN_ID_NOT_UNIQUE);
		}
				
		BigInteger idMsuser = (BigInteger) idMsusers.get(0).get("d0");
		return daoFactory.getUserDao().getUserByIdMsUser(idMsuser.longValue());
	}

	private void validateFullname(String fullname, AuditContext audit) {
		if (StringUtils.isBlank(fullname)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"nama"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		int nameMinimalLength = 3;
		
		if (fullname.length() < nameMinimalLength) {
			throw new ParameterException(getMessage("businesslogic.global.minlength", new Object[] {"nama", nameMinimalLength}, audit), ReasonParam.INVALID_LENGTH);
		}
	}

	private void validateEmail(String email, MsTenant tenant, AuditContext audit) {
		
		if (!"1".equals(tenant.getEmailService()) && StringUtils.isBlank(email)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {PARAM_EMAIL}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if (StringUtils.isNotBlank(email)) {
			String emailRegex = daoFactory.getGeneralSettingDao().getGsValueByCode("AM_EMAIL_FORMAT");
			if (!email.matches(emailRegex)) {
				throw new ParameterException(getMessage("businesslogic.external.emailinvalid", null, audit), ReasonParam.INVALID_FORMAT);
			}
		}
	}

	private void validatePlaceOfBirth(String placeOfBirth, AuditContext audit) {
		if (StringUtils.isBlank(placeOfBirth)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"tmpLahir"}, audit), ReasonParam.MANDATORY_PARAM);
		}
	}

	private void validateDateOfBirth(String dateOfBirth, AuditContext audit) {
		if (StringUtils.isBlank(dateOfBirth)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"tglLahir"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		sdf.setLenient(false);
		try {
			sdf.parse(dateOfBirth);
		} catch (Exception e) {
			throw new ParameterException(getMessage("businesslogic.external.invaliddateformat",
					new String[] {"tglLahir",GlobalVal.DATE_FORMAT}, audit), ReasonParam.INCORRENT_DATE_FORMAT);
		}
	}

	private void validateGender(String gender, AuditContext audit) {
		if (StringUtils.isBlank(gender)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"jenisKelamin"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		if (!GlobalVal.CODE_LOV_FEMALE.equals(gender) && !GlobalVal.CODE_LOV_MALE.equals(gender)) {
			throw new ParameterException(getMessage("businesslogic.user.invalidgender", null, audit), ReasonParam.INVALID_FORMAT);
		}
	}

	private void validatePhoneNumber(String phoneNumber, AuditContext audit) {
		if (StringUtils.isBlank(phoneNumber)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"tlp"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		String phoneRegex = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT);
		if (!phoneNumber.matches(phoneRegex)) {
			throw new ParameterException(getMessage("businesslogic.user.invalidphonenoformat", null, audit), ReasonParam.INVALID_FORMAT);
		}
	}

	private void validateIdNo(String idNo, AuditContext audit) {
		if (StringUtils.isBlank(idNo)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"idKtp"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		if (!StringUtils.isNumeric(idNo)) {
			throw new ParameterException(getMessage("businesslogic.user.nikisnotnumber", null, audit), ReasonParam.NIK_MUST_BE_NUMERIC);
		}
		if (idNo.length() != 16) {
			throw new ParameterException(getMessage("businesslogic.external.idnoinvalid", null, audit), ReasonParam.INVALID_NIK_LENGTH);
		}
	}

	private void validateAddress(String address, AuditContext audit) {
		if (StringUtils.isBlank(address)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"alamat"}, audit), ReasonParam.MANDATORY_PARAM);
		}
	}

	private void validateKecamatan(String kecamatan, AuditContext audit) {
		if (StringUtils.isBlank(kecamatan)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"kecamatan"}, audit), ReasonParam.MANDATORY_PARAM);
		}
	}

	private void validateKelurahan(String kelurahan, AuditContext audit) {
		if (StringUtils.isBlank(kelurahan)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"kelurahan"}, audit), ReasonParam.MANDATORY_PARAM);
		}
	}

	private void validateKota(String kota, AuditContext audit) {
		if (StringUtils.isBlank(kota)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"kota"}, audit), ReasonParam.MANDATORY_PARAM);
		}
	}

	private void validateProvinsi(String provinsi, AuditContext audit) {
		if (StringUtils.isBlank(provinsi)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"provinsi"}, audit), ReasonParam.MANDATORY_PARAM);
		}
	}

	private void validateKodePos(String kodePos, AuditContext audit) {
		if (StringUtils.isBlank(kodePos)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"kodePos"}, audit), ReasonParam.MANDATORY_PARAM);
		}
	}

	private void validateSelfPhoto(String selfPhoto, AuditContext audit) {
		if (StringUtils.isBlank(selfPhoto)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"selfPhoto"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		try {
			MssTool.imageStringToByteArray(selfPhoto);
		} catch (Exception e) {
			throw new ParameterException(getMessage("businesslogic.external.failedprocessingphoto", new String[] {"foto selfie"}, audit), ReasonParam.INVALID_FORMAT);
		}
	}

	private void validateIdPhoto(String idPhoto, AuditContext audit) {
		if (StringUtils.isBlank(idPhoto)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"idPhoto"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		try {
			MssTool.imageStringToByteArray(idPhoto);
		} catch (Exception e) {
			throw new ParameterException(getMessage("businesslogic.external.failedprocessingphoto", new String[] {"foto KTP"}, audit), ReasonParam.INVALID_FORMAT);
		}
	}

	private void validatePassword(String password, MsTenant tenant, AuditContext audit) {
		
		boolean doesNotNeedPassword = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_ACTIVATION);
		if (doesNotNeedPassword) {
			return;
		}
		
		if (StringUtils.isBlank(password)) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"password"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		if (password.length() < 8) {
			throw new ParameterException(getMessage("businesslogic.user.minlengthpassword", null, audit), ReasonParam.INVALID_PASSWORD_LENGTH);
		}
		String passwordRegex = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_PASSWORD_FORMAT);
		if (!password.matches(passwordRegex)) {
			throw new ParameterException(getMessage("businesslogic.user.invalidnewpassword", null, audit), ReasonParam.INVALID_PASSWORD_COMPLEXITY);
		}
	}
	
	private void validateNikPhoneWithDefaultVendor(String nik, String phone, MsVendor vendor, AuditContext audit) {
		String vendorCode = vendor.getVendorCode();
		MsVendorRegisteredUser nikUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(nik, vendorCode);
		MsVendorRegisteredUser phoneUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(phone, vendorCode);
		
		// NIK sudah terpakai
		if (null != nikUser) {
			
			// NIK sudah terpakai, HP belum terpakai
			if (null == phoneUser) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai
			
			// NIK sudah terpakai, HP sudah terpakai, NIK dan HP dipakai orang yang sama
			if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_USER_REGISTERED, null, audit), ReasonRegister.ALREADY_REGISTERED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, NIK dan HP dipakai 2 orang yang berbeda
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		// NIK sbelum terpakai
		
		// NIK belum terpakai, HP sudah terpakai
		if (null != phoneUser) {
			throw new RegisterException("No Telp sudah digunakan oleh NIK yang berbeda dari data yang dikirim", ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		// NIK belum terpakai, HP belum terpakai, bisa registrasi
	}
	
	private void validateInvLinkNikPhoneWithDefaultVendor(String nik, String phone, MsVendor vendor, AuditContext audit) {
		String vendorCode = vendor.getVendorCode();
		MsVendorRegisteredUser nikUser = StringUtils.isBlank(nik) ? null : daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(nik, vendorCode);
		MsVendorRegisteredUser phoneUser = StringUtils.isBlank(phone) ? null : daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(phone, vendorCode);
		
		// NIK sudah terpakai
		if (null != nikUser) {
			
			// NIK sudah terpakai, HP belum terpakai
			if (null == phoneUser) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai
			
			// NIK sudah terpakai, HP sudah terpakai, NIK dan HP dipakai orang yang sama
			if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_USER_REGISTERED, null, audit), ReasonRegister.ALREADY_REGISTERED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, NIK dan HP dipakai 2 orang yang berbeda
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		// NIK sbelum terpakai
		
		// NIK belum terpakai, HP sudah terpakai
		if (null != phoneUser) {
			throw new RegisterException("No Telp sudah digunakan oleh NIK yang berbeda dari data yang dikirim", ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		// NIK belum terpakai, HP belum terpakai, bisa registrasi
	}
	
	private void validateNikPhoneEmailWithDefaultVendor(String nik, String phone, String email, MsVendor vendor, AuditContext audit) {
		String vendorCode = vendor.getVendorCode();
		MsVendorRegisteredUser nikUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(nik, vendorCode);
		MsVendorRegisteredUser phoneUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(phone, vendorCode);
		MsVendorRegisteredUser emailUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, vendorCode);
		
		// NIK sudah terpakai
		if (null != nikUser) {
			// NIK sudah terpakai, HP belum terpakai
			if (null == phoneUser) {
				// NIK sudah terpakai, HP belum terpakai, Email belum terpakai
				if (null == emailUser) {
					throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
				}
				
				// NIK sudah terpakai, HP belum terpakai, Email sudah terpakai, NIK dan Email dipakai orang yang sama
				if (nikUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()) {
					throw new RegisterException(getMessage("businesslogic.register.emailnikusedbyotherphone", null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
				}
				
				// NIK sudah terpakai, HP belum terpakai, Email sudah terpakai, NIK dan Email dipakai 2 orang yang berbeda
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai
			
			// NIK sudah terpakai, HP sudah terpakai, Email belum terpakai
			if (null == emailUser) {
				// NIK sudah terpakai, HP sudah terpakai, Email belum terpakai, NIK dan HP dipakai orang yang sama
				if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser()) {
					throw new RegisterException(getMessage("businesslogic.register.phonenikusedbyotheremail", null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
				}
				
				// NIK sudah terpakai, HP sudah terpakai, Email belum terpakai, NIK dan HP dipakai 2 orang yang berbeda
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK HP dan Email dipakai orang yang sama
			if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser() && phoneUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_USER_REGISTERED, null, audit), ReasonRegister.ALREADY_REGISTERED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK HP dan Email dipakai 3 orang yang berbeda
			if (nikUser.getAmMsuser().getIdMsUser() != phoneUser.getAmMsuser().getIdMsUser()
					&& phoneUser.getAmMsuser().getIdMsUser() != emailUser.getAmMsuser().getIdMsUser()
					&& emailUser.getAmMsuser().getIdMsUser() != nikUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK dan HP digunakan orang yang sama, tapi Email dipakai orang yang berbeda
			if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser()
					&& nikUser.getAmMsuser().getIdMsUser() != emailUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK dan Email digunakan orang yang sama, tapi HP dipakai orang yang berbeda
			if (nikUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()
					&& nikUser.getAmMsuser().getIdMsUser() != phoneUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, HP dan Email digunakan orang yang sama, tapi NIK dipakai orang yang berbeda
			if (phoneUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()
					&& phoneUser.getAmMsuser().getIdMsUser() != nikUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
		}
		
		// NIK belum terpakai
		
		// NIK belum terpakai, HP sudah terpakai
		if (null != phoneUser) {
			// NIK belum terpakai, HP sudah terpakai, Email belum terpakai
			if (null == emailUser) {
				throw new RegisterException(getMessage("businesslogic.register.phoneusedbyothernikemail", null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK belum terpakai, HP sudah terpakai, Email sudah terpakai
			throw new RegisterException(getMessage("businesslogic.register.phoneemailusedbyothernik", null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		// NIK belum terpakai, HP belum terpakai
		
		// NIK belum terpakai, HP belum terpakai, Email sudah terpakai
		if (null != emailUser) {
			throw new RegisterException(getMessage("businesslogic.register.emailusedbyothernikphone", null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		// NIK belum terpakai, HP belum terpakai, Email belum terpakai, lanjut registrasi
	}
	
	private void validateInvLinkNikPhoneEmailWithDefaultVendor(String nik, String phone, String email, MsVendor vendor, AuditContext audit) {
		String vendorCode = vendor.getVendorCode();
		MsVendorRegisteredUser nikUser = StringUtils.isBlank(nik) ? null : daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(nik, vendorCode);
		MsVendorRegisteredUser phoneUser = StringUtils.isBlank(phone) ? null : daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(phone, vendorCode);
		MsVendorRegisteredUser emailUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(email, vendorCode);
		
		// NIK sudah terpakai
		if (null != nikUser) {
			// NIK sudah terpakai, HP belum terpakai
			if (null == phoneUser) {
				// NIK sudah terpakai, HP belum terpakai, Email belum terpakai
				if (null == emailUser) {
					throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
				}
				
				// NIK sudah terpakai, HP belum terpakai, Email sudah terpakai, NIK dan Email dipakai orang yang sama
				if (nikUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()) {
					throw new RegisterException(getMessage("businesslogic.register.emailnikusedbyotherphone", null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
				}
				
				// NIK sudah terpakai, HP belum terpakai, Email sudah terpakai, NIK dan Email dipakai 2 orang yang berbeda
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai
			
			// NIK sudah terpakai, HP sudah terpakai, Email belum terpakai
			if (null == emailUser) {
				// NIK sudah terpakai, HP sudah terpakai, Email belum terpakai, NIK dan HP dipakai orang yang sama
				if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser()) {
					throw new RegisterException(getMessage("businesslogic.register.phonenikusedbyotheremail", null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
				}
				
				// NIK sudah terpakai, HP sudah terpakai, Email belum terpakai, NIK dan HP dipakai 2 orang yang berbeda
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK HP dan Email dipakai orang yang sama
			if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser() && phoneUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_USER_REGISTERED, null, audit), ReasonRegister.ALREADY_REGISTERED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK HP dan Email dipakai 3 orang yang berbeda
			if (nikUser.getAmMsuser().getIdMsUser() != phoneUser.getAmMsuser().getIdMsUser()
					&& phoneUser.getAmMsuser().getIdMsUser() != emailUser.getAmMsuser().getIdMsUser()
					&& emailUser.getAmMsuser().getIdMsUser() != nikUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK dan HP digunakan orang yang sama, tapi Email dipakai orang yang berbeda
			if (nikUser.getAmMsuser().getIdMsUser() == phoneUser.getAmMsuser().getIdMsUser()
					&& nikUser.getAmMsuser().getIdMsUser() != emailUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, NIK dan Email digunakan orang yang sama, tapi HP dipakai orang yang berbeda
			if (nikUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()
					&& nikUser.getAmMsuser().getIdMsUser() != phoneUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK sudah terpakai, HP sudah terpakai, Email sudah terpakai, HP dan Email digunakan orang yang sama, tapi NIK dipakai orang yang berbeda
			if (phoneUser.getAmMsuser().getIdMsUser() == emailUser.getAmMsuser().getIdMsUser()
					&& phoneUser.getAmMsuser().getIdMsUser() != nikUser.getAmMsuser().getIdMsUser()) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE_EMAIL, null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
		}
		
		// NIK belum terpakai
		
		// NIK belum terpakai, HP sudah terpakai
		if (null != phoneUser) {
			// NIK belum terpakai, HP sudah terpakai, Email belum terpakai
			if (null == emailUser) {
				throw new RegisterException(getMessage("businesslogic.register.phoneusedbyothernikemail", null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK belum terpakai, HP sudah terpakai, Email sudah terpakai
			throw new RegisterException(getMessage("businesslogic.register.phoneemailusedbyothernik", null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		// NIK belum terpakai, HP belum terpakai
		
		// NIK belum terpakai, HP belum terpakai, Email sudah terpakai
		if (null != emailUser) {
			throw new RegisterException(getMessage("businesslogic.register.emailusedbyothernikphone", null, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		// NIK belum terpakai, HP belum terpakai, Email belum terpakai, lanjut registrasi
	}
	
	private void validateNikPhoneWithOtherVendor(String nik, String phone, MsVendor vendor, AuditContext audit) {
		Long idNikUser = daoFactory.getUserDao().getIdMsUserRegisteredInOtherVendorByNik(nik, vendor);
		Long idPhoneUser = daoFactory.getUserDao().getIdMsUserRegisteredInOtherVendorByPhone(phone, vendor);
		
		if (null == idNikUser && null != idPhoneUser) {
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		if (null != idNikUser && null != idPhoneUser && !idNikUser.equals(idPhoneUser)) {
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
	}
	
	private void validateNikPhoneEmailWithOtherVendor(String nik, String phone, String email, MsVendor vendor, AuditContext audit) {
		Long idNikUser = daoFactory.getUserDao().getIdMsUserRegisteredInOtherVendorByNik(nik, vendor);
		Long idPhoneUser = daoFactory.getUserDao().getIdMsUserRegisteredInOtherVendorByPhone(phone, vendor);
		Long idEmailUser = daoFactory.getUserDao().getIdMsUserRegisteredInOtherVendorByEmail(email, vendor);
		
		// NIK 0
		if (null == idNikUser) {
			// NIK 0, PHONE 0
			if (null == idPhoneUser) {
				// NIK 0, PHONE 0, EMAIL 1
				if (null != idEmailUser) {
					throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_EMAIL_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
				}
				
				// NIK 0, PHONE 0, EMAIL 0
				return;
			}
			
			// NIK 0, PHONE 1
			
			// NIK 0, PHONE 1, EMAIL 0
			if (null == idEmailUser) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			// NIK 0, PHONE 1, EMAIL 1
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_EMAIL_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		// NIK 1
		
		// NIK 1, PHONE 0
		if (null == idPhoneUser) {
			// NIK 1, PHONE 0, EMAIL 0
			if (null == idEmailUser) {
				return;
			}
			
			// NIK 1, PHONE 0, EMAIL 1
			
			// NIK 1, PHONE 0, EMAIL 1, NIK dan EMAIL dipakai orang yang sama
			if (idEmailUser.equals(idNikUser)) {
				return;
			}
			
			// NIK 1, PHONE 0, EMAIL 1, NIK dan EMAIL dipakai orang yang berbeda
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_EMAIL_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		// NIK 1, PHONE 1
		
		// NIK 1, PHONE 1, EMAIL 0
		if (null == idEmailUser) {
			// NIK 1, PHONE 1, EMAIL 0, NIK dan PHONE dipakai orang yang sama
			if (idNikUser.equals(idPhoneUser)) {
				return;
			}
			
			// NIK 1, PHONE 1, EMAIL 0, NIK dan PHONE dipakai orang yang berbeda
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			
		}
		
		// NIK 1, PHONE 1, EMAIL 1
		boolean nikPhoneSameUser = idNikUser.equals(idPhoneUser);
		boolean nikEmailSameUser = idNikUser.equals(idEmailUser);
		
		if (!nikPhoneSameUser) {
			if (!nikEmailSameUser) {
				throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_EMAIL_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			}
			
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		
		if (!nikEmailSameUser) {
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_EMAIL_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
	}

	@Override
	public void validateNikPhoneEmailForRegister(String nik, String phone, String email, MsVendor vendor, AuditContext audit) {
		if (StringUtils.isBlank(email)) {
			validateNikPhoneWithDefaultVendor(nik, phone, vendor, audit);
			validateNikPhoneWithOtherVendor(nik, phone, vendor, audit);
		} else {
			validateNikPhoneEmailWithDefaultVendor(nik, phone, email, vendor, audit);
			validateNikPhoneEmailWithOtherVendor(nik, phone, email, vendor, audit);
		}
	}

	@Override
	public void validateNikPhoneEmailForRegisterDefaultVendor(String nik, String phone, String email, MsVendor vendor, AuditContext audit) {
		if (StringUtils.isBlank(email)) {
			validateInvLinkNikPhoneWithDefaultVendor(nik, phone, vendor, audit);
		} else {
			validateInvLinkNikPhoneEmailWithDefaultVendor(nik, phone, email, vendor, audit);
		}
	}
	
	private long getDefaultRegisterMaxAttempts() {
		try {
			String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_DEFAULT_VIDA_REGISTER_MAX_ATTEMPTS);
			return Long.valueOf(gsValue);
		} catch (Exception e) {
			return 3L;
		}
	}
	
	private long getRegisterMaxAttempts(MsTenant tenant) {
		// Pakai constant dari AmGlobalKey karena pakai identifer code yang sama
		return tenantSettingsLogic.getSettingValue(tenant, AmGlobalKey.GENERALSETTING_VIDA_REGISTER_MAX_ATTEMPTS, getDefaultRegisterMaxAttempts());
	}

	@Override
	public void validateRegistrationAttemptAmount(TrInvitationLink invLink, AuditContext audit) {

		long maxAttempts = getRegisterMaxAttempts(invLink.getMsTenant());
		long currentRegisterAttempts = invLink.getDailyRegisterAttemptAmount().longValue();
		if (currentRegisterAttempts > maxAttempts) {
			throw new RegisterException(getMessage("businesslogic.register.maxattemptsreached", new Object[] {maxAttempts}, audit), ReasonRegister.MAX_ATTEMPTS_REACHED);
		}
	}

	@Override
	public Status getValidateNikPhoneEmailForRegisterMessage(String nik, String phone, String email, MsVendor vendor, AuditContext audit) {
		try {
			validateNikPhoneEmailForRegister(nik, phone, email, vendor, audit);
		} catch (RegisterException e) {
			Status status = new Status();
			status.setCode(e.getErrorCode());
			status.setMessage(e.getMessage());
			return status;
		}
		return new Status();
	}

	@Override
	public AmMsuser validateGetUserByEmailv2NewTrx(String email, boolean checkUserExistence, AuditContext audit) {
		List<Map<String, Object>> idMsusers = daoFactory.getUserDao().getDistinctIdMsusersByEmailNewTrx(email);
		
		// If id_ms_user not found
		if (CollectionUtils.isEmpty(idMsusers)) {
			if (checkUserExistence) {
				throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_EMAILNOTFOUND, new Object[] {email}, audit), ReasonUser.USER_NOT_FOUND);
			}
			return null;
		}
		
		// If there is more than 1 id_ms_user
		if (idMsusers.size() > 1) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_EMAILUSED_BY_MANY,new Object[] {email, idMsusers.size()}, audit),
					ReasonUser.LOGIN_ID_NOT_UNIQUE);
		}
		
		BigInteger idMsuser = (BigInteger) idMsusers.get(0).get("d0");
		return daoFactory.getUserDao().getUserByIdMsUserNewTrx(idMsuser.longValue());
	}

	@Override
	public void validateUserByPhoneWithEmail(long idMsUserByPhone, String phoneNo, String email, boolean checkUserExistence,
			AuditContext audit) {
		AmMsuser userByEmailToValidate = validateGetUserByEmailv2(email, checkUserExistence, audit);
		if (null == userByEmailToValidate) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND_WITH_EMAIL, 
					new Object[] { email }, audit), ReasonUser.USER_NOT_FOUND_WITH_THAT_EMAIL);
		}
		
		if (idMsUserByPhone != userByEmailToValidate.getIdMsUser()) {
			throw new UserException(getMessage("businesslogic.user.userwithphonenotregisteredwithemail", 
					new Object[] { phoneNo, email }, audit), ReasonUser.USER_WITH_PHONE_NOT_REGISTERED_WITH_EMAIL);
		}
		
	}

	@Override
	public MsVendorRegisteredUser validateGetVrUserByNikForActivationLinkExternal(String idKtp, AuditContext audit) {
		if (StringUtils.isBlank(idKtp)) {
			throw new UserManagementException(getMessage("businesslogic.external.idempty", null , audit),
					ReasonUserManagement.LOGIN_ID_IS_NOT_FOUND);
		}
		
		if (idKtp.length() != 16) {
			throw new UserException(this.messageSource.getMessage("businesslogic.user.invalidniklength", null,
					this.retrieveLocaleAudit(audit)), ReasonUser.INVALID_NIK_LENGTH);
		}
		
		if (!StringUtils.isNumeric(idKtp)) {
			throw new ParameterException(getMessage("businesslogic.user.nikisnotnumber", null, audit), ReasonParam.NIK_MUST_BE_NUMERIC);
		}	
		MsVendorRegisteredUser vUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserByidNo(idKtp);

		
		if (null == vUser) {
			throw new UserManagementException(getMessage("businesslogic.external.idnotfound", new Object[] {idKtp}, audit),
					ReasonUserManagement.USER_NOT_FOUND_WITH_THAT_ID_NO);
		}
		
		if ("1".equals(vUser.getIsActive())) {
			throw new UserException(getMessage("businesslogic.user.useralreadyactivated", null, audit),
					ReasonUser.USER_ALREADY_ACTIVATED);
		}
		return vUser;
	}

	@Override
	public MsVendorRegisteredUser getLatestVendorRegisteredUserByEmail(String email, boolean checkUserExistence, AuditContext audit) {
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserBySignerRegisteredEmail(email);
		if (null == vendorUser && checkUserExistence) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_EMAILNOTFOUND, new Object[] {email}, audit), ReasonUser.USER_NOT_FOUND);
		}
		return vendorUser;
	}

	@Override
	public MsVendorRegisteredUser getLatestVendorRegisteredUserByPhone(String phone, boolean checkUserExistence, AuditContext audit) {
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserByPhoneNumber(phone);
		if (null == vendorUser && checkUserExistence) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_PHONENOTFOUND, new Object[] {phone}, audit), ReasonUser.USER_NOT_FOUND);
		}
		return vendorUser;
	}
	
	private boolean keepCriticalDataOnly(MsTenant tenant) {
		MsTenantSettings setting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "KEEP_CRITICAL_USER_DATA_ONLY");
		if (null == setting) {
			return false;
		}
		
		return "1".equals(setting.getSettingValue());
	}
	
	private boolean keepSelfie(MsTenant tenant) {
		MsTenantSettings setting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "KEEP_SELFIE_PHOTO");
		if (null == setting) {
			return false;
		}
		
		return "1".equals(setting.getSettingValue());
	}

	@Override
	public void removeUnnecessaryRegisterExternalParam(RegisterExternalRequest request, MsTenant tenant) {
		if (!keepCriticalDataOnly(tenant)) {
			return;
		}
		
		request.setTmpLahir(null);
		request.setTglLahir(null);
		request.setJenisKelamin(null);
		request.setAlamat(null);
		request.setKecamatan(null);
		request.setKelurahan(null);
		request.setKota(null);
		request.setProvinsi(null);
		request.setKodePos(null);
		
		if (!keepSelfie(tenant)) {
			request.setSelfPhoto(null);
		}
		
		request.setIdPhoto(null);
		
	}

	@Override
	public void removeUnnecessaryRegisterParam(UserBean userData, MsTenant tenant) {
		if (!keepCriticalDataOnly(tenant)) {
			return;
		}
		
		userData.setUserPob(null);
		userData.setUserDob(null);
		userData.setUserGender(null);
		userData.setUserAddress(null);
		userData.setKecamatan(null);
		userData.setKelurahan(null);
		userData.setKota(null);
		userData.setProvinsi(null);
		userData.setZipcode(null);
		
		if (!keepSelfie(tenant)) {
			userData.setSelfPhoto(null);
		}
		
		userData.setIdPhoto(null);
	}
	
	private boolean isCertifExpired(MsVendorRegisteredUser vendorUser) {
		if (vendorUser.getCertExpiredDate() == null ) {
			return true;
		} else 
		{
			Date currentDate = MssTool.changeDateFormat(new Date(), GlobalVal.DATE_FORMAT);
			Date expiredDate = MssTool.changeDateFormat(vendorUser.getCertExpiredDate(), GlobalVal.DATE_FORMAT);
			return currentDate.getTime() >= expiredDate.getTime();
		}
		
	}
	
	private boolean isCertifPoaExpired(MsVendorRegisteredUser vendorUser) {
		if (vendorUser.getCertPoaExpiredDate() == null ) {
			return true;
		} else 
		{
			Date currentDate = MssTool.changeDateFormat(new Date(), GlobalVal.DATE_FORMAT);
			Date expiredDate = MssTool.changeDateFormat(vendorUser.getCertPoaExpiredDate(), GlobalVal.DATE_FORMAT);
			return currentDate.getTime() >= expiredDate.getTime();
		}
		
	}

	@Override
	public boolean isCertifExpiredForSign(MsVendorRegisteredUser vendorUser, AuditContext audit) {
		
		if (!"1".equals(vendorUser.getIsActive())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_HASNOT_ACTIVATED, null, audit), ReasonUser.USER_HAS_NOT_ACTIVATED);
		}
		
		if (!GlobalVal.VENDOR_CODE_VIDA.equals(vendorUser.getMsVendor().getVendorCode())) {
			return false;
		}
		
		return isCertifExpired(vendorUser);
	}

	@Override
	public boolean isCertifExpiredForRegister(MsVendorRegisteredUser vendorUser, AuditContext audit) {
		
		if (!GlobalVal.VENDOR_CODE_VIDA.equals(vendorUser.getMsVendor().getVendorCode())) {
			return false;
		}
		
		return isCertifExpired(vendorUser);
	}

	@Override
	public boolean isCertifExpiredForInquiry(MsVendorRegisteredUser vendorUser) {
		return isCertifExpired(vendorUser);
	}
	
	@Override
	public boolean isCertifPoaExpiredForInquiry(MsVendorRegisteredUser vendorUser) {
		return isCertifPoaExpired(vendorUser);
	}

	@Override
	public Status validateNikPhoneEmailForRegisterDefaultVendorStatus(String nik, String phone, String email,
			MsVendor vendor, AuditContext audit) {
		try {
			validateNikPhoneEmailForRegisterDefaultVendor(nik, phone, email, vendor, audit);
		} catch (RegisterException e) {
			Status status = new Status();
			status.setCode(e.getErrorCode());
			status.setMessage(e.getMessage());
			return status;
		}
		
		return new Status();
	}	

	@Override
	public boolean validateNikEmailPhone (MsVendorRegisteredUser vru,String nik, String email, String phone,AuditContext audit) {
		
		if(vru == null ) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"Vendor Registered User"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		if(StringUtils.isBlank(nik) ) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"NIK"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if(StringUtils.isBlank(phone) ) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"Phone"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if (!"1".equals(vru.getEmailService()) && StringUtils.isBlank(email) ) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {PARAM_EMAIL}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if (!personalDataEncryptionLogic.decryptToString(vru.getPhoneBytea()).equals(phone)) {
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_EMAIL_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
		}
		if (!"1".equals(vru.getEmailService()) && !vru.getSignerRegisteredEmail().equalsIgnoreCase(email)) {
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_EMAIL_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			
		}
		
		AmUserPersonalData personalData = daoFactory.getUserDao().getUserPersonalDataByIdMsUser(vru.getAmMsuser());
		if (!personalDataEncryptionLogic.decryptToString(personalData.getIdNoBytea()).equals(nik)) {
			throw new RegisterException(getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_EMAIL_DOES_NOT_BELONG_TO_NIK, new String[] {nik}, audit), ReasonRegister.NIK_PHONE_EMAIL_USED);
			
		}
		
		return true;
	}
}
