package com.adins.exceptions;

import com.adins.framework.exception.StatusCodes;

public class StatusCode extends StatusCodes {
	
	public static final int UNKNOWN = 9999;
	
	public static final int DIGISIGN_FAILED				= 500;
	public static final int TEKEN_AJA_ERROR				= 600;
	public static final int PAJAKKU_ERROR				= 700;
	public static final int VIDA_ERROR					= 800;
	public static final int PRIVY_ERROR					= 900;
	
	public static final int GENERIC_AM	= 1_050;
	public static final int CHANGE_PASSWORD_VIOLATION 	= GENERIC_AM + 1;
	public static final int INVALID_OLD_PASSWORD 		= GENERIC_AM + 2;
	public static final int INVALID_NEW_PASSWORD		= GENERIC_AM + 3;
	public static final int INACTIVE_USER				= GENERIC_AM + 4;
	public static final int INVALID_USER				= GENERIC_AM + 5;
	public static final int INCORRECT_RESET_CODE		= GENERIC_AM + 6;
	public static final int INVALID_PASSWORD_LENGTH		= GENERIC_AM + 7;
	public static final int INVALID_PASSWORD_COMPLEXITY = GENERIC_AM + 8;
	public static final int EMPTY_PASSWORD 				= GENERIC_AM + 9;
	
	// EKYC Error
	public static final int EKYC_NIK_TIDAK_SESUAI_KETENTUAN 			= 1;
	public static final int EKYC_NAMA_LENGKAP_TIDAK_SESUAI_KETENTUAN 	= 2;
	public static final int EKYC_TEMPAT_LAHIR_TIDAK_SESUAI_KETENTUAN 	= 3;
	public static final int EKYC_TGL_LAHIR_TIDAK_SESUAI_KETENTUAN 		= 4;
	public static final int EKYC_FOTO_TIDAK_SESUAI_KETENTUAN 			= 5;
	public static final int EKYC_CUSTOMER_ID_TIDAK_SESUAI_KETENTUAN 	= 6;
	public static final int EKYC_TRX_ID_TIDAK_SESUAI_KETENTUAN 			= 7;
	public static final int EKYC_TRX_SOURCE_TIDAK_SESUAI_KETENTUAN 		= 8;
	public static final int EKYC_REQUEST_TIDAK_DAPAT_DITERUSKAN 		= 30;
	public static final int EKYC_KESALAHAN_PROSES_DUKCAPIL 				= 31;
	public static final int EKYC_KESALAHAN_PROSES_FACE_RECOGNITION 		= 32;
	
	//login 11xx
	public static final int LOGIN 					= GENERIC_AM + 100;
	public static final int LOGIN_CONCURRENCE 		= LOGIN + 1;
	public static final int LOGIN_DORMANT 			= LOGIN + 2;
	public static final int LOGIN_INACTIVE 			= LOGIN + 3;
	public static final int LOGIN_INVALID 			= LOGIN + 4;
	public static final int LOGIN_LOCKED 			= LOGIN + 5;
	public static final int LOGIN_PWDEXPIRED 		= LOGIN + 6;
	public static final int LOGIN_CHANGEPASSWORD	= LOGIN + 7;
	public static final int LOGIN_REQUIRED			= LOGIN + 8;
	
	//common 2xxx
	public static final int GENERIC_COMMON_ERROR	= 2_000;
	public static final int INVALID_IDENTIFIER		= GENERIC_COMMON_ERROR + 1;
	public static final int INVALID_SCRIPT			= GENERIC_COMMON_ERROR + 2;
	public static final int TEMPLATE_MISMATCH		= GENERIC_COMMON_ERROR + 3;
	public static final int ERROR_PARSING			= GENERIC_COMMON_ERROR + 4;
	public static final int ERROR_GENERATE			= GENERIC_COMMON_ERROR + 5;
	public static final int ERROR_EXIST				= GENERIC_COMMON_ERROR + 6;
	public static final int ERROR_FORM				= GENERIC_COMMON_ERROR + 7;
	public static final int ERROR_CHANGE			= GENERIC_COMMON_ERROR + 8;
	public static final int ERROR_REMOTE			= GENERIC_COMMON_ERROR + 9;
	public static final int ERROR_NOT_EXIST			= GENERIC_COMMON_ERROR + 10;
	public static final int ERROR_SUBMIT			= GENERIC_COMMON_ERROR + 11;
	public static final int ERROR_MAX_EXCEEDED		= GENERIC_COMMON_ERROR + 12;
	public static final int ERROR_TEMPLATE			= GENERIC_COMMON_ERROR + 13;
	public static final int ERROR_RV_ALREADY_USED	= GENERIC_COMMON_ERROR + 14;
	public static final int INVALID_DATE_RANGE		= GENERIC_COMMON_ERROR + 15;
	public static final int ALGORITHM_NOT_FOUND		= GENERIC_COMMON_ERROR + 16;
	public static final int INVALID_FORMAT			= GENERIC_COMMON_ERROR + 17;
	public static final int MANDATORY_PARAMETER		= GENERIC_COMMON_ERROR + 18;
	public static final int INVALID_DATE_FORMAT		= GENERIC_COMMON_ERROR + 19;
	public static final int MUST_BE_NUMERIC			= GENERIC_COMMON_ERROR + 20;
	public static final int INVALID_CONDITION		= GENERIC_COMMON_ERROR + 21;
	public static final int INVALID_LENGTH			= GENERIC_COMMON_ERROR + 22;
	public static final int INVALID_VALUE			= GENERIC_COMMON_ERROR + 23;
	public static final int SUBMIT_ERROR			= GENERIC_COMMON_ERROR + 50;
	public static final int TENANT_SETTING_NOT_CONFIGURED = GENERIC_COMMON_ERROR + 51;
	public static final int SUBMIT_DELETED		   	= SUBMIT_ERROR + 1;
	public static final int SUBMIT_INVALID_DATA		= SUBMIT_ERROR + 2; //error code 2052 untuk save draft failed task
	
	//document 4000 - 4199
	public static final int DOCUMENT_SERVICES					= 4_000;
	public static final int REFERENCE_NO_NOT_EXISTS 			= DOCUMENT_SERVICES + 1;
	public static final int DOCUMENT_FILE_NOT_EXISTS 			= DOCUMENT_SERVICES + 2;
	public static final int SIGN_ACTION_NOT_EXISTS 				= DOCUMENT_SERVICES + 3;
	public static final int SIGNER_TYPE_NOT_EXISTS 				= DOCUMENT_SERVICES + 4;
	public static final int NAME_NOT_EXISTS 					= DOCUMENT_SERVICES + 5;
	public static final int EMAIL_NOT_EXISTS 					= DOCUMENT_SERVICES + 6;
	public static final int DOCUMENT_TEMPLATE_CODE_NOT_EXISTS 	= DOCUMENT_SERVICES + 7;
	public static final int STAMP_DUTY_UNAVAILABLE				= DOCUMENT_SERVICES + 8;
	public static final int READ_WRITE_PDF_ERROR				= DOCUMENT_SERVICES + 9;
	public static final int TENANT_NOT_EXISTS					= DOCUMENT_SERVICES + 10;
	public static final int OFFICE_NOT_EXISTS					= DOCUMENT_SERVICES + 11;
	public static final int DOCUMENT_FILE_INACCESSIBLE			= DOCUMENT_SERVICES + 12;
	public static final int SIGN_STATUS_NOT_EXISTS				= DOCUMENT_SERVICES + 13;
	public static final int VENDOR_NOT_EXISTS					= DOCUMENT_SERVICES + 14;
	public static final int CANNOT_ACCESS_OTHER_TENANT_DOC		= DOCUMENT_SERVICES + 15;
	public static final int INVALID_INQUIRY_DOC_TYPE			= DOCUMENT_SERVICES + 16;
	public static final int DOC_TYPE_NOT_EXIST					= DOCUMENT_SERVICES + 17;
	public static final int DOCUMENT_ID_EMPTY					= DOCUMENT_SERVICES + 18;
	public static final int DOCUMENT_NOT_YET_SIGNED_ALL 		= DOCUMENT_SERVICES + 19;
	public static final int DOCUMENT_ALREADY_SIGNED_ALL 		= DOCUMENT_SERVICES + 20;
	public static final int DOCUMENT_EXCEL_ERROR				= DOCUMENT_SERVICES + 21;
	public static final int INACTIVE_AGREEMENT					= DOCUMENT_SERVICES + 22;
	public static final int INVALID_SEND_DOCUMENT_SIGNER		= DOCUMENT_SERVICES + 23;
	public static final int DOCUMENT_ALRDY_SIGNED				= DOCUMENT_SERVICES + 24;
	public static final int DOCUMENT_NEED_SIGNED				= DOCUMENT_SERVICES + 25;
	public static final int VAR_EMPTY							= DOCUMENT_SERVICES + 26;
	public static final int DOC_NOMINAL_INVALID					= DOCUMENT_SERVICES + 27;
	public static final int INVALID_ID							= DOCUMENT_SERVICES + 28;
	public static final int TENANT_TRX_ID_EXIST					= DOCUMENT_SERVICES + 29;
	public static final int PERURI_DOC_TYPE_NOT_EXIST			= DOCUMENT_SERVICES + 30;
	public static final int TEMPLATE_NO_SDT						= DOCUMENT_SERVICES + 31;
	public static final int EMPTY_DOCUMENT_ID					= DOCUMENT_SERVICES + 32;
	public static final int DOCUMENT_NOT_FOUND					= DOCUMENT_SERVICES + 33;
	public static final int INVALID_DOCUMENT_VENDOR				= DOCUMENT_SERVICES + 34;
	public static final int INVALID_DOCUMENT_SIGNER				= DOCUMENT_SERVICES + 35;
	public static final int AUTOSIGN_FAILED						= DOCUMENT_SERVICES + 36;
	public static final int DOCUMENT_IS_BEING_SIGNED 			= DOCUMENT_SERVICES + 37;
	public static final int CONTRACT_IS_IN_STAMPING_PROCESS 	= DOCUMENT_SERVICES + 38;
	public static final int REFERENCE_NO_EMPTY					= DOCUMENT_SERVICES + 39;
	public static final int CONTRACT_ALREADY_SIGNED_AND_STAMPED = DOCUMENT_SERVICES + 40;
	public static final int CONTRACT_ALREADY_SIGNED				= DOCUMENT_SERVICES + 41;
	public static final int CONTRACT_IS_IN_SIGNING_PROCESS		= DOCUMENT_SERVICES + 42;
	public static final int USER_IS_NOT_THE_SIGNER_OF_THE_CONTRACT = DOCUMENT_SERVICES + 43;
	public static final int DIFFERENT_REFF_NO					= DOCUMENT_SERVICES + 44;
	public static final int DOC_FILE_INVALID					= DOCUMENT_SERVICES + 45;
	public static final int DOCUMENT_TEMPLATE_CODE_EMPTY		= DOCUMENT_SERVICES + 46;
	public static final int DOCUMENT_NO_STAMP					= DOCUMENT_SERVICES + 47;
	public static final int SIGNER_NOT_ACTIVATED				= DOCUMENT_SERVICES + 48;
	public static final int CANT_SEQ_SIGN						= DOCUMENT_SERVICES + 49;
	public static final int CANNOT_AUTOSIGN						= DOCUMENT_SERVICES + 50;
	public static final int MAX_SIGN_EXCEEDED					= DOCUMENT_SERVICES + 51;
	public static final int UPLOAD_DOC_FAILED					= DOCUMENT_SERVICES + 52;
	public static final int SEQ_SIGN_NOT_UNIQUE					= DOCUMENT_SERVICES + 53;
	public static final int SEQ_NO_EMPTY						= DOCUMENT_SERVICES + 54;
	public static final int CANNOT_STAMP						= DOCUMENT_SERVICES + 55;
	public static final int DOCUMENT_NAME_EMPTY					= DOCUMENT_SERVICES + 56;
	public static final int CANNOT_SEND_NOTIF					= DOCUMENT_SERVICES + 57;
	public static final int DOCUMENT_NOT_APPROPRIATE			= DOCUMENT_SERVICES + 58;
	public static final int DOCUMENT_NOT_BELONG_TO_REF_NUMBER 	= DOCUMENT_SERVICES + 59;
	public static final int DOCUMENT_HASH_EMPTY 				= DOCUMENT_SERVICES + 60;
	public static final int CERT_EMPTY							= DOCUMENT_SERVICES + 61;
	public static final int BRANCH_NOT_EXIST    				= DOCUMENT_SERVICES + 62;
	public static final int DOCUMENT_OWNER_NOT_EXISTS			= DOCUMENT_SERVICES + 63;
	public static final int NOT_HIGHEST_PRIORITY				= DOCUMENT_SERVICES + 64;
	public static final int DOCUMENT_NOT_BELONG_TO_TENANT		= DOCUMENT_SERVICES + 65;
	public static final int CUSTOM_SIGN_NOT_EXIST				= DOCUMENT_SERVICES + 66;
	
	// Embed webview 4200 - 4299
	public static final int EMBED_WEBVIEW_ERROR	= 4_200;
	public static final int WEBVIEW_DECRYPT_ERROR 		= EMBED_WEBVIEW_ERROR + 1;
	public static final int WEBVIEW_SIGN_LINK_NOT_FOUND = EMBED_WEBVIEW_ERROR + 2;
	
  	// Stamp duty 5xxx
	public static final int STAMP_DUTY_ERROR			= 5_000;
	public static final int USER_CANNOT_CREATE_SDT		= STAMP_DUTY_ERROR + 1;
	public static final int INVALID_VENDOR_TENANT		= STAMP_DUTY_ERROR + 2;
	public static final int VENDOR_CANNOT_CREATE_SDT	= STAMP_DUTY_ERROR + 3;
	public static final int GENERATE_SDT_REPORT_ERROR	= STAMP_DUTY_ERROR + 4;
	public static final int RETRY_STAMPING_TENANT_CODE_INVALID	= STAMP_DUTY_ERROR + 5;
	public static final int RETRY_STAMPING_DOCUMENT_NOT_FOUND	= STAMP_DUTY_ERROR + 6;
	public static final int RETRY_STAMPING_DECRYPT_FAILED		= STAMP_DUTY_ERROR + 7;
	public static final int BALANCE_MUTATION_NOT_FOUND			= STAMP_DUTY_ERROR + 8;
	public static final int REF_NUMBER_INVALID			= STAMP_DUTY_ERROR + 9;
	public static final int RETRY_STAMPING_FAILED		= STAMP_DUTY_ERROR + 10;
	public static final int RETRY_STAMPING_PROCESS 		= STAMP_DUTY_ERROR + 11;
	public static final int START_STAMPING_INVALID		= STAMP_DUTY_ERROR + 12;

	// E-Meterai Stamping Process 505x
	public static final int EMETERAI_ERROR				= 5_050;
	public static final int EMETERAI_LOGIN_EXCEPTION				= EMETERAI_ERROR + 1;
	public static final int EMETERAI_LOGIN_RESPONSE_ERROR			= EMETERAI_ERROR + 2;
	public static final int EMETERAI_UPLOAD_DOC_EXCEPTION			= EMETERAI_ERROR + 3;
	public static final int EMETERAI_UPLOAD_DOC_RESPONSE_ERROR		= EMETERAI_ERROR + 4;
	public static final int EMETERAI_GENERATE_EXCEPTION				= EMETERAI_ERROR + 5;
	public static final int EMETERAI_GENERATE_RESPONSE_ERROR		= EMETERAI_ERROR + 6;
	public static final int EMETERAI_STAMPING_EXCEPTION				= EMETERAI_ERROR + 7;
	public static final int EMETERAI_STAMPING_RESPONSE_ERROR		= EMETERAI_ERROR + 8;
	public static final int EMETERAI_DOWNLOAD_DOC_EXCEPTION			= EMETERAI_ERROR + 9;
	public static final int EMETERAI_DOWNLOAD_DOC_RESPONSE_ERROR	= EMETERAI_ERROR + 10;
	public static final int EMETERAI_INVALID_CREDENTIAL				= EMETERAI_ERROR + 11;
	public static final int EMETERAI_PROCESS_FAILED					= EMETERAI_ERROR + 12;
	
	// Payment Sign type 51xx
	public static final int PAYMENT_SIGN_TYPE_ERROR		= 5_100;
	public static final int PAYMENT_SIGN_TYPE_EMPTY_TENANT_CODE = PAYMENT_SIGN_TYPE_ERROR + 1;
	public static final int PAYMENT_SIGN_TYPE_TENANT_NOT_FOUND	= PAYMENT_SIGN_TYPE_ERROR + 2;
	public static final int PAYMENT_SIGN_TYPE_USER_NOT_FOUND	= PAYMENT_SIGN_TYPE_ERROR + 3;
	public static final int PAYMENT_SIGN_TYPE_INVALID_ROLE		= PAYMENT_SIGN_TYPE_ERROR + 4;
	public static final int PAYMENT_SIGN_TYPE_BALANCE_NULL		= PAYMENT_SIGN_TYPE_ERROR + 5;
	
	// Vendor 52xx
	public static final int VENDOR_ERROR	= 5_200;
	public static final int VENDOR_TYPE_CODE_INVALID	= VENDOR_ERROR + 1;
	public static final int VENDOR_TYPE_CODE_EMPTY		= VENDOR_ERROR + 2;
	public static final int VENDOR_TENANT_CODE_EMPTY	= VENDOR_ERROR + 3;
	public static final int VENDOR_TENANT_NOT_EXIST		= VENDOR_ERROR + 4;
	public static final int VENDOR_USER_NOT_FOUND		= VENDOR_ERROR + 5;
	public static final int VENDOR_USE_TENANT_NOT_FOUND	= VENDOR_ERROR + 6;
	public static final int VENDOR_CODE_INVALID			= VENDOR_ERROR + 7;
	public static final int VENDOR_NOT_FOUND			= VENDOR_ERROR + 8;
	public static final int VENDOR_CAN_NOT_RESEND_ACT_LINK 	= VENDOR_ERROR + 9;
	public static final int VENDOR_BALANCE_VENDOR_NOT_FOUND = VENDOR_ERROR + 10;
	public static final int VENDOR_NAME_EMPTY 			= VENDOR_ERROR + 11;
	public static final int VENDOR_STATUS_INVALID 		= VENDOR_ERROR + 12;
	public static final int VENDOR_PSRE_PAYEMENT_SIGN_TYPE_INVALID = VENDOR_ERROR + 13;
	public static final int INVALID_VENDOR_OFTENANT 	= VENDOR_ERROR + 14;
	public static final int VENDOR_NOT_SUPPORT_HASH_SIGN = VENDOR_ERROR + 15;
	public static final int VENDOR_NOT_SUPPORT_DOWNLOAD_CERT = VENDOR_ERROR + 16;
	public static final int VENDOR_NOT_EXIST_OR_ACTIVE_IN_TENANT = VENDOR_ERROR + 17;
	public static final int PSRE_LIST_EMPTY				= VENDOR_ERROR + 18;
	public static final int VENDOR_NOT_OPERATING		= VENDOR_ERROR + 19;
	
	// Email 5301 - 5350
	public static final int EMAIL_ERROR			= 5_300;
	public static final int SEND_EMAIL_ERROR	= EMAIL_ERROR + 1;
	public static final int READ_EMAIL_ERROR	= EMAIL_ERROR + 2;
	public static final int DELETE_EMAIL_ERROR	= EMAIL_ERROR + 3;
	public static final int CREATE_EMAIL_ERROR	= EMAIL_ERROR + 4;
	
	// SMS 5351 - 5400
	public static final int SMS_ERROR		= 5_350;
	public static final int SEND_SMS_ERROR	= SMS_ERROR + 1;
	
	// Embed Msg Encrypt Decrypt Error 54xx
	public static final int EMBED_MSG_ERROR	= 5_400;
	public static final int EMBED_MSG_EMPTY 	= EMBED_MSG_ERROR + 1;
	public static final int EMBED_MSG_INVALID 	= EMBED_MSG_ERROR + 2;
	public static final int EMBED_SESSION_EXPIRED = EMBED_MSG_ERROR + 3;
	
	//LOV
	public static final int LOV_MSG_ERROR	= 5_450;
	public static final int LOV_CODE_INVALID	= LOV_MSG_ERROR + 1;
	
	// Tenant 55xx
	public static final int TENANT_ERROR	= 5_500;
	public static final int TENANT_CODE_EMPTY					= TENANT_ERROR + 1;
	public static final int TENANT_NOT_FOUND					= TENANT_ERROR + 2;
	public static final int TENANT_REMINDER_RECEIVER_TOO_LONG	= TENANT_ERROR + 3;
	public static final int TENANT_CODE_EXISTED					= TENANT_ERROR + 4;
	public static final int TENANT_BALANCE_TYPE_NOT_FOUND		= TENANT_ERROR + 5;
	public static final int TENANT_INVALID_THRESHOLD			= TENANT_ERROR + 6;
	public static final int TENANT_LIST_BALANCE_EMPTY			= TENANT_ERROR + 7;
	public static final int TENANT_BALANCE_TYPE_EMTPY 			= TENANT_ERROR + 8;
	public static final int TENANT_VALIDATE_API_KEY_ERROR		= TENANT_ERROR + 9;
	public static final int TENANT_API_KEY_EMPTY				= TENANT_ERROR + 10;
	public static final int TENANT_API_KEY_INVALID				= TENANT_ERROR + 11;
	public static final int TENANT_CODE_INVALID					= TENANT_ERROR + 12;
	public static final int TENANT_LIVENESS_FACE_COMPARE_SERVICES_NOT_ACTIVE 	= TENANT_ERROR + 13;
	public static final int TENANT_WITH_TENANT_CODE_NOT_REGISTERED				= TENANT_ERROR + 14;
	public static final int INCORRECT_API_KEY									= TENANT_ERROR + 15;
	public static final int TENANT_CALLBACK_URL_INVALID			= TENANT_ERROR + 16;
	public static final int INVALID_USE_WA_MESSAGE				= TENANT_ERROR + 17;
	public static final int INVALID_CLIENT_URL					= TENANT_ERROR + 18;
	public static final int MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND = TENANT_ERROR + 19;
	public static final int REGION_CODE_EMPTY = TENANT_ERROR + 20;
	public static final int BUSINESS_LINE_NOT_FOUND = TENANT_ERROR + 21;
	public static final int TENANT_VENDOR_CODE_EMPTY = TENANT_ERROR + 22;
	
	// Error History 56xx
	public static final int ERR_HIST_ERROR		= 5_600;
	public static final int ERR_HIST_NOT_FOUND	= ERR_HIST_ERROR + 1;
	
	// Location (Province, District, Sub District) 57xx
	public static final int LOCATION_ERROR		= 5_700;
	public static final int PROVINCE_NOT_FOUND		= LOCATION_ERROR + 1;
	public static final int DISTRICT_NOT_FOUND		= LOCATION_ERROR + 2;
	public static final int SUBDISTRICT_NOT_FOUND	= LOCATION_ERROR + 3;
	public static final int PROVINCE_NAME_EMPTY		= LOCATION_ERROR + 4;
	public static final int DISTRICT_NAME_EMPTY		= LOCATION_ERROR + 5;
	
	// Saldo 6xxx
	public static final int SALDO_ERROR = 6_000;
	public static final int SALDO_BALANCE_TYPE_CODE_EMPTY 		= SALDO_ERROR + 1;
	public static final int SALDO_BALANCE_TYPE_NAME_EMPTY 		= SALDO_ERROR + 2;
	public static final int SALDO_BALANCE_TYPE_ALREADY_EXIST 	= SALDO_ERROR + 3;
	public static final int SALDO_BALANCE_TYPE_NOT_FOUND	 	= SALDO_ERROR + 4;
	public static final int DECRYPT_FAILED						= SALDO_ERROR + 5;
	public static final int BALANCE_NOT_ENOUGH					= SALDO_ERROR + 6;
	public static final int BALANCE_NOT_CONFIGURED				= SALDO_ERROR + 7;
	
	// Invitation Link 7xxx
	public static final int INVITATION_LINK_ERROR = 7_000;
	public static final int INV_LINK_NOT_EXIST				= INVITATION_LINK_ERROR + 1;
	public static final int PROCESS_FOR_INV_BY_EMAIL_ONLY	= INVITATION_LINK_ERROR + 2;
	public static final int CANNOT_SEND_OTP_TO_EMAIL		= INVITATION_LINK_ERROR + 3;
	public static final int CANNOT_VERIFY_OTP_TO_EMAIL		= INVITATION_LINK_ERROR + 4;
	public static final int INVALID_INVITATION_LINK			= INVITATION_LINK_ERROR + 5;
	public static final int INACTIVE_LINK					= INVITATION_LINK_ERROR + 6;
	public static final int INV_LINK_INVALID_EMAIL			= INVITATION_LINK_ERROR + 7;
	public static final int INV_LINK_INVALID_PHONE			= INVITATION_LINK_ERROR + 8;
	public static final int INV_LINK_USER_REGISTERED		= INVITATION_LINK_ERROR + 9;
	public static final int INV_LINK_INACTIVE				= INVITATION_LINK_ERROR + 10;
	public static final int INVALID_INV_BY					= INVITATION_LINK_ERROR + 11;
	public static final int INVALID_RECEIVER_DETAIL			= INVITATION_LINK_ERROR + 12;
	public static final int GENERATE_IL_REPORT_ERROR        = INVITATION_LINK_ERROR + 13;
	public static final int MISMATCH_RECEIVER_DETAIL		= INVITATION_LINK_ERROR + 14;
	public static final int DOUBLE_NIK						= INVITATION_LINK_ERROR + 15;
	public static final int DOUBLE_EMAIL					= INVITATION_LINK_ERROR + 16;
	public static final int DOUBLE_PHONE					= INVITATION_LINK_ERROR + 17;
	public static final int CANNOT_SEND_OTP_TO_PHONE		= INVITATION_LINK_ERROR + 18;
	public static final int DECRYPT_CODE_ERROR				= INVITATION_LINK_ERROR + 19;
	public static final int EMPTY_VENDOR					= INVITATION_LINK_ERROR + 20;
	public static final int CANNOT_UPDATE					= INVITATION_LINK_ERROR + 21;
	public static final int MISMATCH_VENDOR					= INVITATION_LINK_ERROR + 22;
	public static final int INVITATION_EXPIRED				= INVITATION_LINK_ERROR + 23;
	public static final int CANNOT_REGENERATE				= INVITATION_LINK_ERROR + 24;
	public static final int RECEIVER_DETAIL_EMPTY			= INVITATION_LINK_ERROR + 25;
	public static final int ACTIVE_ECERT					= INVITATION_LINK_ERROR + 26;
	public static final int DOCUMENT_NOT_FOUND_INV			= INVITATION_LINK_ERROR + 27;
	public static final int PHONE_NO_NULL 					= INVITATION_LINK_ERROR + 28;
	//user 8xxx	
	public static final int USER_SERVICES	= 8_100;
	public static final int USER_SERVICES_DETECTED 							= USER_SERVICES + 1;
	public static final int LOGIN_ID_NOT_UNIQUE								= USER_SERVICES + 2;
	public static final int LOGIN_ID_NOT_EXISTS     						= USER_SERVICES + 3;
	public static final int GEN_SIGN_SPECIMEN_FAILED						= USER_SERVICES + 4;
	public static final int LOGIN_ID_EMPTY									= USER_SERVICES + 5;
	public static final int USER_ALREADY_REGISTERED							= USER_SERVICES + 6;
	public static final int OTP_REGISTER_EMAIL_ERROR						= USER_SERVICES + 7;
	public static final int OTP_EMPTY										= USER_SERVICES + 8;
	public static final int OTP_INVALID										= USER_SERVICES + 9;
	public static final int USER_ROLE_INVALID								= USER_SERVICES + 10;
	public static final int PERSONAL_DATA_NOT_FOUND							= USER_SERVICES + 11;
	public static final int INQ_USER_TYPE_EMPTY								= USER_SERVICES + 12;
	public static final int INQ_USER_TYPE_INVALID							= USER_SERVICES + 13;
	public static final int SEND_USER_INFO_EMAIL_ERROR						= USER_SERVICES + 14;
	public static final int PHONE_NUM_ALREADY_EXIST							= USER_SERVICES + 15;
	public static final int INVALID_NIK_LENGTH								= USER_SERVICES + 16;
	public static final int RESET_PASSWORD_CODE_INVALID						= USER_SERVICES + 17;
	public static final int LIVENESS_CHECK_FAILED 							= USER_SERVICES + 18;
	public static final int MAX_RESET_PASSWORD_REACHED 						= USER_SERVICES + 19;
	public static final int USER_NIK_ALREADY_EXISTED						= USER_SERVICES + 20;
	public static final int PASSWORD_CANNOT_BE_EMPTY						= USER_SERVICES + 21;
	public static final int PHONE_NUM_EMPTY									= USER_SERVICES + 22;
	public static final int USER_NIK_EMPTY									= USER_SERVICES + 23; 
	public static final int EMAIL_OR_PHONE_EMPTY							= USER_SERVICES + 24;
	public static final int IS_VERIFY_EMAIL_EMPTY							= USER_SERVICES + 25;
	public static final int VERIFY_NOT_MATCH								= USER_SERVICES + 26;
	public static final int NOT_REGISTERED									= USER_SERVICES + 27;
	public static final int INVALID_GENDER_CODE								= USER_SERVICES + 28;
	public static final int INVALID_UPDATE_CODE								= USER_SERVICES + 29;
	public static final int USER_TENANT_NOT_FOUND							= USER_SERVICES + 30;
	public static final int ALREADY_ACTIVATED								= USER_SERVICES + 31;
	public static final int UNREGISTERED_PHONE								= USER_SERVICES + 32;
	public static final int UNREGISTERED_EMAIL								= USER_SERVICES + 33;
	public static final int UNREGISTERED_NIK								= USER_SERVICES + 34;
	public static final int TOKEN_NOT_MATCH			    					= USER_SERVICES + 35;
	public static final int USER_NOT_FOUND									= USER_SERVICES + 36;
	public static final int USER_CAN_REREGISTER								= USER_SERVICES + 37;
	public static final int CHECK_REGISTER_NOT_MATCHED  					= USER_SERVICES + 38;
	public static final int ROLE_CODE_NOT_FOUND								= USER_SERVICES + 39;
	public static final int IS_USER_MANAGEMENT_INVALID 						= USER_SERVICES + 40;
	public static final int ROLE_CODE_NOT_AVAILABLE_IN_TENANT 				= USER_SERVICES + 41;
	public static final int INVALID_TENANT_AND_CALLER						= USER_SERVICES + 42;
	public static final int INVALID_ROLE_AND_USER_AND_TENANT 				= USER_SERVICES + 43;
	public static final int ID_NO_ALREADY_REGISTERED 						= USER_SERVICES + 44;
	public static final int USER_NOT_REGISTERED_IN_VENDOR 					= USER_SERVICES + 45;
	public static final int USER_NOT_FOUND_WITH_THAT_EMAIL 					= USER_SERVICES + 46;
	public static final int SIGNER_EMAIL_EMPTY 								= USER_SERVICES + 47;
	public static final int VENDOR_CODE_EMPTY 								= USER_SERVICES + 48;
	public static final int PASSWORD_NOT_MATCH								= USER_SERVICES + 49;
	public static final int MAX_OTP_ACTIVATION_USER_REACHED 				= USER_SERVICES + 50;
	public static final int USER_HAS_NOT_REGISTERED 						= USER_SERVICES + 51;
	public static final int USER_ALREADY_ACTIVATED							= USER_SERVICES + 52;
	public static final int USER_NOT_FOUND_WITH_THAT_PHONE_NO 				= USER_SERVICES + 53;
	public static final int PHONE_NOT_MATCH_WITH_INVITATION 				= USER_SERVICES + 54;
	public static final int USER_HAS_NOT_ACTIVATED 							= USER_SERVICES + 55;
	public static final int USER_NOT_REGISTERED_IN_TENANT 					= USER_SERVICES + 56;
	public static final int USER_DOES_NOT_HAVE_ANY_DOCUMENT					= USER_SERVICES + 57;
	public static final int MAX_LIVENESS_FACECOMPARE_REACHED 				= USER_SERVICES + 58;
	public static final int PHOTO_NOT_FOUND 								= USER_SERVICES + 59;
	public static final int LIVENESS_FAILED 								= USER_SERVICES + 60;
	public static final int FACE_COMPARE_FAILED 							= USER_SERVICES + 61;
	public static final int LIVENESS_AND_FACE_COMPARE_FAILED 				= USER_SERVICES + 62;
	public static final int NIK_IS_NOT_NUMBER 								= USER_SERVICES + 63;
	public static final int EMAIL_ALREADY_REGISTERED 						= USER_SERVICES + 64;
	public static final int USER_NOT_FOUND_WITH_THAT_ID_NO 					= USER_SERVICES + 65;
	public static final int USER_NAME_NOT_MATCH 							= USER_SERVICES + 66;
	public static final int EMAIL_EMPTY										= USER_SERVICES + 67;
	public static final int TYPE_DATA_INVALID 								= USER_SERVICES + 67;
	public static final int USER_WITH_EMAIL_NOT_REGISTERED_WITH_THAT_PHONE 	= USER_SERVICES + 68;
	public static final int USER_WITH_PHONE_NOT_REGISTERED_WITH_THAT_EMAIL 	= USER_SERVICES + 69;
	public static final int MAX_REGISTER_ATTEMPTS_REACHED 					= USER_SERVICES + 70;
	public static final int OTP_EXPIRED										= USER_SERVICES + 71;
	public static final int SEND_EMAIL_CERTIFICATE_NOTIFICATION_ERROR		= USER_SERVICES + 72;
	public static final int SEND_SMS_CERTIFICATE_NOTIFICATION_ERROR			= USER_SERVICES + 73;
	public static final int UNHANDLED_VENDOR								= USER_SERVICES + 74;
	public static final int NOT_NEED_ACTIVATE_VENDOR 						= USER_SERVICES + 75;
	public static final int URL_FORWORDER_CODE_INVALID 						= USER_SERVICES + 76;
	public static final int URL_FORWARDER_EXPIRED 							= USER_SERVICES + 77;
	public static final int INVITATION_NOT_RESENT 							= USER_SERVICES + 78;
	public static final int RESET_CODE_EMPTY	 							= USER_SERVICES + 78;
	public static final int OTP_SENDING_MEDIA_NOT_VALID						= USER_SERVICES + 79;
	public static final int USER_REGISTERED_WITHOUT_EMAIL 					= USER_SERVICES + 80;
	public static final int USER_CERTIFICATE_EXPIRED 						= USER_SERVICES + 81;

	// Job 9xxx
	public static final int JOB_ERROR = 9_000;
	public static final int JOB_TYPE_CODE_EMPTY 	= JOB_ERROR + 1;
	public static final int JOB_RESULT_NOT_FOUND	= JOB_ERROR + 2;
	public static final int ID_JOB_RESULT_EMPTY		= JOB_ERROR + 3;
	
	// Sign confirmation 
	public static final int SIGN_CONFIRMATION_DOCUMENT 	= 9_100;
	public static final int EMPTY_IP_ADDRESS 			= SIGN_CONFIRMATION_DOCUMENT + 1;
	public static final int EMPTY_BROWSER 				= SIGN_CONFIRMATION_DOCUMENT + 2;
	public static final int INVALID_VENDOR 				= SIGN_CONFIRMATION_DOCUMENT + 3;
	public static final int INVALID_REQUEST_DUPLICATE 	= SIGN_CONFIRMATION_DOCUMENT + 4;
	public static final int INVALID_USER_SIGNER 		= SIGN_CONFIRMATION_DOCUMENT + 5;
	public static final int MUST_OTP 					= SIGN_CONFIRMATION_DOCUMENT + 6;
	public static final int EMPTY_PHONE_NO 				= SIGN_CONFIRMATION_DOCUMENT + 7;
	
	//system 9x5x
	public static final int ENTITY_NOT_FOUND	= GENERIC_DATABASE + 3;
	public static final int OVER_LIMIT_API_RATE	= GENERIC_DATABASE + 4;
	public static final int DUPLICATE_REQUEST	= GENERIC_SYSTEM + 9;
	
	//usermanagement 10xxx
	public static final int USER_MANAGEMENT_ERROR 		= 10_000;
	public static final int TENANT_ROLE_NOT_EXIST 		= USER_MANAGEMENT_ERROR + 1;
	public static final int ROLE_IS_NOT_USER_MANAGEMENT = USER_MANAGEMENT_ERROR + 2;
	public static final int NO_PHONE_IS_NOT_VALID 		= USER_MANAGEMENT_ERROR + 3;
	public static final int KTP_DIGIT_IS_NOT_VALID 		= USER_MANAGEMENT_ERROR + 4;
	public static final int EMAIL_IS_NOT_VALID 			= USER_MANAGEMENT_ERROR + 5;
	public static final int INVALID_LOGIN_ID 			= USER_MANAGEMENT_ERROR + 6;
	public static final int ROLE_IS_NOT_ADM_CLIENT 		= USER_MANAGEMENT_ERROR+ 7;
	public static final int USER_IS_NOT_ACTIVE 			= USER_MANAGEMENT_ERROR + 8;
	public static final int CALLER_ID_IS_NOT_TENANT_OF_LOGIN_ID = USER_MANAGEMENT_ERROR + 9;
	public static final int ROLE_IS_NOT_FOUND 					= USER_MANAGEMENT_ERROR + 10;
	public static final int SAME_NO_KTP 						= USER_MANAGEMENT_ERROR + 11;
	
	//Autosign
	public static final int AUTOSIGN_ERROR 							= 20_000;
	public static final int BASE64NOTVALID							= AUTOSIGN_ERROR + 1;		
	public static final int EXECUTIONDATENOTVALID 					= AUTOSIGN_ERROR + 2;
	public static final int EMPTYBASE64 							= AUTOSIGN_ERROR + 3;
	public static final int EMPTYFILENAME 							= AUTOSIGN_ERROR + 4;
	public static final int EMPTYEXECUTIONTIME						= AUTOSIGN_ERROR + 5;
	public static final int INVALID_IMPORT_AUTOSIGN_DATE_RANGE		= AUTOSIGN_ERROR + 6;
	public static final int HEADER_NOT_FOUND						= AUTOSIGN_ERROR + 7;
	public static final int INVALID_STATUS_IMPORT 					= AUTOSIGN_ERROR + 8;
	public static final int EMPTY_TEMPLATE_FILE 					= AUTOSIGN_ERROR + 9;

	// Tenant Settings
	public static final int TENANT_SETTINGS_ERROR 							= 30_000;
	public static final int TENANT_SETTINGS_NOT_FOUND 						= TENANT_SETTINGS_ERROR + 1;

	// General Settings
	public static final int GENERAL_SETTING_ERROR 							= 40_000;
	public static final int GENERAL_SETTING_NOT_FOUND  						= GENERAL_SETTING_ERROR + 1;

	// Notification Testing
	public static final int SEND_NOTIFICATION_TESTING_ERROR 				= 50_000;
	public static final int SEND_NOTIFICATION_MEDIA_NOT_VALID  				= SEND_NOTIFICATION_TESTING_ERROR + 1;
	public static final int NOTIF_GATEWAY_AND_MESSAGE_MEDIA_NOT_VALID		= SEND_NOTIFICATION_TESTING_ERROR + 2;
	
	// Menu
	public static final int MENU_ERROR 				= 51_000;
	public static final int MENU_NOT_ACTIVE			= MENU_ERROR + 1;
	public static final int PATH_EMPTY				= MENU_ERROR + 2;
	public static final int MENU_NOT_MANAGEABLE		= MENU_ERROR + 3;
	public static final int MENU_NOT_FOUND			= MENU_ERROR + 4;
	
	// Role
	public static final int ROLE_ERROR 				= 51_200;
	public static final int ADMESIGN_NOT_ALLOWED	= ROLE_ERROR + 1;

}
