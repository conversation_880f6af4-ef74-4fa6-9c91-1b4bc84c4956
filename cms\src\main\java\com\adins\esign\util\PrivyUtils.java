package com.adins.esign.util;

import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Date;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class PrivyUtils {
	
	private static final String CONST_HMAC_SHA256 = "HmacSHA256";
	
	private PrivyUtils() {
		throw new IllegalStateException("Utility class");
	}
	
	public static String createSignature(Date timestamp, String privyUsername, String privyPassword, String json) throws NoSuchAlgorithmException, InvalidKeyException {
		
		JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
		jsonObject.getAsJsonObject().remove("ktp");
		jsonObject.getAsJsonObject().remove("identity");
		jsonObject.getAsJsonObject().remove("selfie");
		jsonObject.getAsJsonObject().remove("supporting_docs");
		jsonObject.getAsJsonObject().remove("document");
		String formattedJson = jsonObject.toString().replace(" ", "");
		
		String formattedTimestamp = MssTool.formatDateToStringIn(timestamp, "yyyy-MM-dd'T'HH:mm:ssXXX");
		String apiKey = privyUsername;
		String secretKey = privyPassword;
		String method = "POST";
		
		MessageDigest md = MessageDigest.getInstance("MD5");
		byte[] hash = md.digest(formattedJson.getBytes(StandardCharsets.UTF_8));
		String bodyMd5 = Base64.getEncoder().encodeToString(hash);
		
		String hmacSignature = formattedTimestamp + ":" + apiKey + ":" + method + ":" + bodyMd5;
		
		Mac mac = Mac.getInstance(CONST_HMAC_SHA256);
		SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), CONST_HMAC_SHA256);
		mac.init(secretKeySpec);
		byte[] hmacSha256Bytes = mac.doFinal(hmacSignature.getBytes(StandardCharsets.UTF_8));
		
		String hmacBase64 = Base64.getEncoder().encodeToString(hmacSha256Bytes);
		String authString = apiKey + ":" + hmacBase64;
		return Base64.getEncoder().encodeToString(authString.getBytes(StandardCharsets.UTF_8));
	}
	
	public static String createLivenessSignature(Date timestamp, String privyUsername, String privyPassword, String merchantKey, String json) throws NoSuchAlgorithmException, InvalidKeyException {
		
		String formattedTimestamp = MssTool.formatDateToStringIn(timestamp, "yyyy-MM-dd'T'HH:mm:ssZ");
		String username = privyUsername;
		String password = privyPassword;
		String method = "GET";
		
		String jsonBody = json;
		
		MessageDigest md = MessageDigest.getInstance("MD5");
		byte[] hash = md.digest(jsonBody.getBytes(StandardCharsets.UTF_8));
		String bodyMd5 = Base64.getEncoder().encodeToString(hash);
		
		String hmacSignature = formattedTimestamp + ":" + username + ":" + method + ":" + bodyMd5;
		
		Mac mac = Mac.getInstance(CONST_HMAC_SHA256);
		SecretKeySpec secretKeySpec = new SecretKeySpec(password.getBytes(StandardCharsets.UTF_8), CONST_HMAC_SHA256);
		mac.init(secretKeySpec);
		byte[] hmacSha256Bytes = mac.doFinal(hmacSignature.getBytes(StandardCharsets.UTF_8));
		String hmacBase64 = Base64.getEncoder().encodeToString(hmacSha256Bytes);
		
		String signature = "#" + merchantKey + ":#" + hmacBase64;
		return Base64.getEncoder().encodeToString(signature.getBytes(StandardCharsets.UTF_8));
		
	}
	
}
