	package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class DocumentException extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public enum ReasonDocument {
		REFERENCE_NO_NOT_EXISTS,
		DOCUMENT_TEMPLATE_CODE_NOT_EXISTS,
		DOCUMENT_FILE_NOT_EXISTS,
		DOCUMENT_FILE_INACCESSIBLE,
		SIGN_ACTION_NOT_EXISTS,
		SIGNER_TYPE_NOT_EXISTS,
		TENANT_NOT_EXISTS,
		VENDOR_NOT_EXISTS,
		OFFICE_NOT_EXISTS,
		NAME_NOT_EXISTS,
		EMAIL_NOT_EXISTS,
		SIGN_STATUS_NOT_EXISTS,
		ERROR_EXIST,
		PARAM_INVALID,
		SDT_UNAVAILABLE,
		READ_WRITE_ERROR,
		INVALID_DATE_RANGE,
		CANNOT_ACCESS_OTHER_TENANT,
		INVALID_INQUIRY_TYPE,
		DOC_TYPE_NOT_EXIST,
		DOCUMENT_ID_EMPTY,
		DOCUMENT_NOT_YET_SIGNED_ALL,
		DOCUMENT_ALREADY_SIGNED_ALL,
		DOCUMENT_EXCEL_ERROR,
		INACTIVE_AGREEMENT,
		INVALID_SEND_DOCUMENT_SIGNER,
		DOCUMENT_ALRDY_SIGNED,
		VAR_EMPTY,
		DOC_NOMINAL_INVALID,
		INVALID_ID,
		TENANT_TRX_ID_EXIST,
		PERURI_DOC_TYPE_NOT_EXIST,
		TEMPLATE_NO_SDT,
		EMPTY_DOCUMENT_ID,
		DOCUMENT_NOT_FOUND,
		INVALID_DOCUMENT_VENDOR,
		INVALID_DOCUMENT_SIGNER,
		AUTOSIGN_FAILED,
		CONTRACT_IS_IN_STAMPING_PROCESS,
		REFERENCE_NO_EMPTY,
		CONTRACT_ALREADY_SIGNED_AND_STAMPED,
		CONTRACT_ALREADY_SIGNED,
		CONTRACT_IS_IN_SIGNING_PROCESS,
		USER_IS_NOT_THE_SIGNER_OF_THE_CONTRACT,
		DIFFERENT_REFF_NO,
		DOC_FILE_INVALID,
		DOCUMENT_TEMPLATE_CODE_EMPTY,
		DOCUMENT_NO_STAMP,
		SIGNER_NOT_ACTIVATED,
		CANT_SEQ_SIGN,
		CANNOT_AUTOSIGN,
		MAX_SIGN_EXCEEDED,
		UPLOAD_DOC_FAILED,
		SEQ_SIGN_NOT_UNIQUE,
		SEQ_NO_EMPTY,
		CANNOT_STAMP,
		DOCUMENT_NAME_EMPTY,
		CANNOT_SEND_NOTIF,
		DOCUMENT_NOT_BELONG_TO_REF_NUMBER,
		DOCUMENT_HASH_EMPTY,
		CERT_EMPTY,	
		BRANCH_NOT_EXIST,
		DOCUMENT_OWNER_NOT_EXISTS,
		NOT_HIGHEST_PRIORITY,
		DOCUMENT_NOT_BELONG_TO_TENANT,
		CUSTOM_SIGN_NOT_EXIST,
		UNKNOWN
	}

	private final ReasonDocument reason;
	
	public DocumentException(ReasonDocument reason) {
		this.reason = reason;
	}
	
	public DocumentException(String message, ReasonDocument reason) {
		super(message);
		this.reason = reason;
	}

	public DocumentException(Throwable ex, ReasonDocument reason) {
		super(ex);
		this.reason = reason;
	}
	
	public DocumentException(String message, Throwable ex, ReasonDocument reason) {
		super(message, ex);
		this.reason = reason;
	}

	public ReasonDocument getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case ERROR_EXIST:
				return StatusCode.ERROR_EXIST;
			case REFERENCE_NO_NOT_EXISTS:
				return StatusCode.REFERENCE_NO_NOT_EXISTS;
			case DOCUMENT_TEMPLATE_CODE_NOT_EXISTS:
				return StatusCode.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS;
			case DOCUMENT_FILE_NOT_EXISTS:
				return StatusCode.DOCUMENT_FILE_NOT_EXISTS;
			case DOCUMENT_FILE_INACCESSIBLE:
				return StatusCode.DOCUMENT_FILE_INACCESSIBLE;
			case SIGN_ACTION_NOT_EXISTS:
				return StatusCode.SIGN_ACTION_NOT_EXISTS;
			case SIGNER_TYPE_NOT_EXISTS:
				return StatusCode.SIGNER_TYPE_NOT_EXISTS;
			case TENANT_NOT_EXISTS:
				return StatusCode.TENANT_NOT_EXISTS;
			case VENDOR_NOT_EXISTS:
				return StatusCode.VENDOR_NOT_EXISTS;
			case OFFICE_NOT_EXISTS:
				return StatusCode.OFFICE_NOT_EXISTS;
			case NAME_NOT_EXISTS:
				return StatusCode.NAME_NOT_EXISTS;
			case EMAIL_NOT_EXISTS:
				return StatusCode.EMAIL_NOT_EXISTS;
			case SIGN_STATUS_NOT_EXISTS:
				return StatusCode.SIGN_STATUS_NOT_EXISTS;
			case PARAM_INVALID:
				return StatusCode.INVALID_IDENTIFIER;
			case SDT_UNAVAILABLE:
				return StatusCode.STAMP_DUTY_UNAVAILABLE;
			case READ_WRITE_ERROR:
				return StatusCode.READ_WRITE_PDF_ERROR;
			case INVALID_DATE_RANGE:
				return StatusCode.INVALID_DATE_RANGE;
			case CANNOT_ACCESS_OTHER_TENANT:
				return StatusCode.CANNOT_ACCESS_OTHER_TENANT_DOC;
			case INVALID_INQUIRY_TYPE:
				return StatusCode.INVALID_INQUIRY_DOC_TYPE;
			case DOC_TYPE_NOT_EXIST:
				return StatusCode.DOC_TYPE_NOT_EXIST;
			case DOCUMENT_ID_EMPTY:
				return StatusCode.DOCUMENT_ID_EMPTY;
			case DOCUMENT_NOT_YET_SIGNED_ALL:
				return StatusCode.DOCUMENT_NOT_YET_SIGNED_ALL;
			case DOCUMENT_ALREADY_SIGNED_ALL:
				return StatusCode.DOCUMENT_ALREADY_SIGNED_ALL;
			case DOCUMENT_EXCEL_ERROR:
				return StatusCode.DOCUMENT_EXCEL_ERROR;
			case INACTIVE_AGREEMENT:
				return StatusCode.INACTIVE_AGREEMENT;
			case INVALID_SEND_DOCUMENT_SIGNER:
				return StatusCode.INVALID_SEND_DOCUMENT_SIGNER;
			case DOCUMENT_ALRDY_SIGNED:
				return StatusCode.DOCUMENT_ALRDY_SIGNED;
			case VAR_EMPTY:
				return StatusCode.VAR_EMPTY;
			case DOC_NOMINAL_INVALID:
				return StatusCode.DOC_NOMINAL_INVALID;
			case INVALID_ID:
				return StatusCode.INVALID_ID;
			case TENANT_TRX_ID_EXIST:
				return StatusCode.TENANT_TRX_ID_EXIST;
			case PERURI_DOC_TYPE_NOT_EXIST:
				return StatusCode.PERURI_DOC_TYPE_NOT_EXIST;
			case TEMPLATE_NO_SDT:
				return StatusCode.TEMPLATE_NO_SDT;
			case EMPTY_DOCUMENT_ID:
				return StatusCode.EMPTY_DOCUMENT_ID;
			case DOCUMENT_NOT_FOUND:
				return StatusCode.DOCUMENT_NOT_FOUND;
			case INVALID_DOCUMENT_VENDOR:
				return StatusCode.INVALID_DOCUMENT_VENDOR;
			case INVALID_DOCUMENT_SIGNER:
				return StatusCode.INVALID_DOCUMENT_SIGNER;
			case AUTOSIGN_FAILED:
				return StatusCode.AUTOSIGN_FAILED;
			case CONTRACT_IS_IN_STAMPING_PROCESS:
				return StatusCode.CONTRACT_IS_IN_STAMPING_PROCESS;
			case REFERENCE_NO_EMPTY:
				return StatusCode.REFERENCE_NO_EMPTY;
			case CONTRACT_ALREADY_SIGNED_AND_STAMPED:
				return StatusCode.CONTRACT_ALREADY_SIGNED_AND_STAMPED;
			case CONTRACT_ALREADY_SIGNED:
				return StatusCode.CONTRACT_ALREADY_SIGNED;
			case CONTRACT_IS_IN_SIGNING_PROCESS:
				return StatusCode.CONTRACT_IS_IN_SIGNING_PROCESS;
			case USER_IS_NOT_THE_SIGNER_OF_THE_CONTRACT:
				return StatusCode.USER_IS_NOT_THE_SIGNER_OF_THE_CONTRACT;
			case DIFFERENT_REFF_NO:
				return StatusCode.DIFFERENT_REFF_NO;
			case DOC_FILE_INVALID:
				return StatusCode.DOC_FILE_INVALID;
			case DOCUMENT_TEMPLATE_CODE_EMPTY:
				return StatusCode.DOCUMENT_TEMPLATE_CODE_EMPTY;
			case DOCUMENT_NO_STAMP:
				return StatusCode.DOCUMENT_NO_STAMP;
			case SIGNER_NOT_ACTIVATED:
				return StatusCode.SIGNER_NOT_ACTIVATED;
			case CANT_SEQ_SIGN:
				return StatusCode.CANT_SEQ_SIGN;
			case CANNOT_AUTOSIGN:
				return StatusCode.CANNOT_AUTOSIGN;
			case MAX_SIGN_EXCEEDED:
				return StatusCode.MAX_SIGN_EXCEEDED;
			case UPLOAD_DOC_FAILED:
				return StatusCode.UPLOAD_DOC_FAILED;
			case SEQ_SIGN_NOT_UNIQUE:
				return StatusCode.SEQ_SIGN_NOT_UNIQUE;
			case SEQ_NO_EMPTY:
				return StatusCode.SEQ_NO_EMPTY;
			case CANNOT_STAMP:
				return StatusCode.CANNOT_STAMP;
			case DOCUMENT_NAME_EMPTY:
				return StatusCode.DOCUMENT_NAME_EMPTY;
			case CANNOT_SEND_NOTIF:
				return StatusCode.CANNOT_SEND_NOTIF;
			case DOCUMENT_NOT_BELONG_TO_REF_NUMBER:
				return StatusCode.DOCUMENT_NOT_BELONG_TO_REF_NUMBER;
			case DOCUMENT_HASH_EMPTY:
				return StatusCode.DOCUMENT_HASH_EMPTY;
			case CERT_EMPTY:
				return StatusCode.CERT_EMPTY;
			case BRANCH_NOT_EXIST:
				return StatusCode.BRANCH_NOT_EXIST;
			case DOCUMENT_OWNER_NOT_EXISTS:
				return StatusCode.DOCUMENT_OWNER_NOT_EXISTS;
			case NOT_HIGHEST_PRIORITY:
				return StatusCode.NOT_HIGHEST_PRIORITY;
			case DOCUMENT_NOT_BELONG_TO_TENANT:
				return StatusCode.DOCUMENT_NOT_BELONG_TO_REF_NUMBER;
			case CUSTOM_SIGN_NOT_EXIST:
				return StatusCode.CUSTOM_SIGN_NOT_EXIST;
			default:
				return StatusCode.UNKNOWN;
			}
			
		}
		return StatusCode.UNKNOWN;
	}

}
