package com.adins.esign.model.custom;

import java.io.Serializable;

public class InquiryDocumentBean implements Serializable{
	private static final long serialVersionUID = 1L;
	private String refNumber;
	private String docTypeName;
	private String docTemplateName;
	private String customerName;
	private String requestDate;
	private String completeDate;
	private String documentId;
	private String totalSigned;
	private String signStatus;
	private long idDocumentD;
	private String officeName;
	private String regionName;
	private String totalStamped;
	private String statusOtomatisStamping;
	private String statusProsesMaterai;
	private String canStartStamp;
	private String vendorCode;
	private String signingProcess;
	private String canRetryStamp;
	private String isActive;
	private String isCurrentTopPriority;

	public String getCanStartStamp() {
		return canStartStamp;
	}
	public void setCanStartStamp(String canStartStamp) {
		this.canStartStamp = canStartStamp;
	}
	public String getStatusProsesMaterai() {
		return statusProsesMaterai;
	}
	public void setStatusProsesMaterai(String statusProsesMaterai) {
		this.statusProsesMaterai = statusProsesMaterai;
	}
	public String getStatusOtomatisStamping() {
		return statusOtomatisStamping;
	}
	public void setStatusOtomatisStamping(String statusOtomatisStamping) {
		this.statusOtomatisStamping = statusOtomatisStamping;
	}
	public String getOfficeName() {
		return officeName;
	}
	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}
	public String getRegionName() {
		return regionName;
	}
	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}
	public String getRefNumber() {
		return refNumber;
	}
	public void setRefNumber(String refNumber) {
		this.refNumber = refNumber;
	}
	public String getDocTypeName() {
		return docTypeName;
	}
	public void setDocTypeName(String docTypeName) {
		this.docTypeName = docTypeName;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getRequestDate() {
		return requestDate;
	}
	public void setRequestDate(String requestDate) {
		this.requestDate = requestDate;
	}
	public String getCompleteDate() {
		return completeDate;
	}
	public void setCompleteDate(String completeDate) {
		this.completeDate = completeDate;
	}
	public String getDocumentId() {
		return documentId;
	}
	public void setDocumentId(String documentId) {
		this.documentId = documentId;
	}
	public String getSignStatus() {
		return signStatus;
	}
	public void setSignStatus(String signStatus) {
		this.signStatus = signStatus;
	}
	public String getTotalSigned() {
		return totalSigned;
	}
	public void setTotalSigned(String totalSigned) {
		this.totalSigned = totalSigned;
	}
	public String getDocTemplateName() {
		return docTemplateName;
	}
	public void setDocTemplateName(String docTemplateName) {
		this.docTemplateName = docTemplateName;
	}
	public long getIdDocumentD() {
		return idDocumentD;
	}
	public void setIdDocumentD(long idDocumentD) {
		this.idDocumentD = idDocumentD;
	}
	public String getTotalStamped() {
		return totalStamped;
	}
	public void setTotalStamped(String totalStamped) {
		this.totalStamped = totalStamped;
	}
	public String getVendorCode() {
		return vendorCode;
	}
	public void setVendorCode(String vendorCode) {
		this.vendorCode = vendorCode;
	}
	public String getSigningProcess() {
		return signingProcess;
	}
	public void setSigningProcess(String signingProcess) {
		this.signingProcess = signingProcess;
	}
	public String getCanRetryStamp() {
		return canRetryStamp;
	}
	public void setCanRetryStamp(String canRetryStamp) {
		this.canRetryStamp = canRetryStamp;
	}
	public String getIsActive() {
		return isActive;
	}
	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}
	public String getIsCurrentTopPriority() {
		return isCurrentTopPriority;
	}
	public void setIsCurrentTopPriority(String isCurrentTopPriority) {
		this.isCurrentTopPriority = isCurrentTopPriority;
	}
	
}
