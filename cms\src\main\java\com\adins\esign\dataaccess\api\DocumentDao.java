package com.adins.esign.dataaccess.api;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentDStampduty;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrDocumentHStampdutyError;
import com.adins.esign.model.TrDocumentSigningRequest;
import com.adins.esign.model.TrManualReport;
import com.adins.esign.model.TrPsreSigningConfirmation;
import com.adins.esign.model.custom.ActivationDocumentBean;
import com.adins.esign.model.custom.CheckDocumentBeforeSigningBean;
import com.adins.esign.model.custom.DeleteOnPremResultBean;
import com.adins.esign.model.custom.DocHBean;
import com.adins.esign.model.custom.DocumentTemplateBean;
import com.adins.esign.model.custom.DocumentTemplateByTenantBean;
import com.adins.esign.model.custom.DocumentTemplateSignLocationBean;
import com.adins.esign.model.custom.ListReportBean;
import com.adins.esign.model.custom.SignerInfoBean;
import com.adins.esign.model.custom.TaxReportBean;
import com.adins.esign.webservices.model.ActivationStatusByDocumentId;
import com.adins.esign.webservices.model.GetListReportRequest;

public interface DocumentDao {
//DOCUMENT TEMPLATE
	List<DocumentTemplateBean> getListDocumentTemplate(String searchDocTempCode, String searchDocTempName,
			String searchIsActive, String tenantCode, int page, int pageSize);

	List<DocumentTemplateByTenantBean> getListDocumentTemplateByTenant(long idTenant);

	BigInteger countListDocumentTemplate(String searchDocTempCode, String searchDocTempName, String searchIsActive,
			String tenantCode);

	void insertDocumentTemplate(MsDocTemplate docTemplate);

	MsDocTemplate getDocumentTemplateByCodeAndTenantCode(String docTemplateCode, String tenantCode);
	
	MsDocTemplate getDocumentTemplateByCodeTenantCodeAndVendorCode(String docTemplateCode, String tenantCode, String vendorCode);

	void updateDocumentTemplate(MsDocTemplate docTemplate);

//SIGN LOCATION
	List<MsDocTemplateSignLoc> getListSignLocationByTemplateCode(String documentTemplateCode);

	List<MsDocTemplateSignLoc> getListSignLocationByTemplateCodeAndIdTenant(String documentTemplateCode, long idTenant);

	List<MsDocTemplateSignLoc> getListSignLocationByTemplateCodeTenantCode(String documentTemplateCode,
			String tenantCode);

	List<MsDocTemplateSignLoc> getListSignLocationByTemplateCodeAndLovSignType(String documentTemplateCode,
			String signTypeCode);

	List<MsDocTemplateSignLoc> getListSignLocation(String documentTemplateCode, String signTypeCode, String tenantCode);
	
	List<MsDocTemplateSignLoc> getListSignLocationByTemplateCodeTenantCodeAndLovSignerType(String documentTemplateCode, String signerTypeCode, String tenantCode);

	List<DocumentTemplateSignLocationBean> getListSignLocationByTemplateCodeV2(String documentTemplateCode,
			long idTenant);

	List<Map<String, Object>> getListStampLocationByTemplateCode(String documentTemplateCode, long idTenant);

	BigInteger countStampNeededByTemplateCode(String documentTemplateCode, long idTenant);

	void insertSignLocation(MsDocTemplateSignLoc signLocation);

	void deleteSignLocation(List<MsDocTemplateSignLoc> signLocationList);

//DOCUMENT D
	TrDocumentD insertDocumentDetail(TrDocumentD doc);
	void insertDocumentDetailNewTrx(TrDocumentD doc);

	void updateDocumentDetail(TrDocumentD doc);
	void updateDocumentDetailNewTran(TrDocumentD doc);
	void updateDocumentDetailNoRollBack(long idDocD, Date completedDate, String signStatus, Date currDate, String usrUpd, short totalSigned);

	TrDocumentD getDocumentDetailByDocId(String docId);
	TrDocumentD getDocumentDetailByDocIdNewTrx(String docId);
	TrDocumentD getDocumentDetailStampingOnlyByDocId(String docId);
	TrDocumentD getDocumentDetailByDocIdNewTran(String docId);
	TrDocumentD getDocumentDetailById(long idDocInserted);
	TrDocumentD getDocumentDetailByDocumentHeaderId(long documentHeaderId);
	TrDocumentD getDocumentDetailByDocumentHeaderIdNewTran(long documentHeaderId);
	TrDocumentD getDocumentDetailByTenantTrxId(String trxId);
	TrDocumentD getLatestDocumentDetailBySignerLoginId(String loginId);
	TrDocumentD getLatestDocumentDetailBySigner(AmMsuser user);
	TrDocumentD getEarliestUnsignedDocumentBySignerLoginId(String loginId);
	TrDocumentD getLatestUnsignedDocument(AmMsuser user, MsTenant tenant, MsVendor vendor);
	TrDocumentD getDocumentByPsreDocumentId(String psreDocumentId);
	TrDocumentD getLatestDocumentDetailBySentDateSigner(AmMsuser amMsuser);

	List<TrDocumentD> getListDocumentDetailByDocumentHeaderId(long documentHeaderId);
	List<TrDocumentD> getListDocumentDetailByDocumentHeaderIdNewTran(long documentHeaderId);
	List<TrDocumentD> getListDocumentStatusPendingByloginId(String loginId);

	String generateDocumentId();
	List<Map<String, Object>> getDocDetailNeedSign(long idDocumentH, long idMsUser);
	BigInteger countOtherDocDetailNeedSign(long idDocumentH, long idDocumentD, long idMsUser);
	List<ActivationDocumentBean> getActivationDocumentByDocHAndUser(TrDocumentH docH, AmMsuser user);
	List<Map<String, Object>> getListInquiryDocument(String[] paramsInquiry, String callerId, String queryType, String msg, boolean isReadOffice);
	long countListInquiryDocument(String[] paramsInquiry, String callerId, String queryType, String msg, boolean isReadOffice);
	List<Map<String, Object>> getDocumentSignDetails(String documentId, AmMsuser user);
	Integer countCheckDocumentSendStatus(String documentId);
	List<ActivationStatusByDocumentId> getActivationStatusByDocumentId(String documentId);
	BigInteger countDocumentInAgreement(Long idDocumentH);
	Short getHighestPriorityUnsignedDocument(Long idDocumentH, Long idMsUser);

	List<CheckDocumentBeforeSigningBean> getListCheckDocumentBeforeSigning(String[] documentId);
	List<CheckDocumentBeforeSigningBean> getListCheckDocumentBeforeSigningFromTrDocumentH(String[] documentId);
	List<CheckDocumentBeforeSigningBean> getListDocIdAndSigningProcessPrivy(String[] documentId, long idMsUser);

	// CheckDocumentEmbed
	List<CheckDocumentBeforeSigningBean> getListCheckDocumentBeforeSigningEmbed(String[] documentId, long idMsUser);

	// getDocVida
	List<Map<String, Object>> getDocDetailNeedSignVida(long idDocumentH, long idMsUser);
	List<Map<String, Object>> getDocDetailNeedSignVidaWithSamePrioritySequenceDocD(long idDocumentH, long idMsUser,long idDocumentD);

//DOCUMENT H
	TrDocumentH getDocumentHeaderByRefNo(String refNo);
	TrDocumentH getDocumentHeaderByRefNoAndTenantCode(String refNo, String tenantCode);
	TrDocumentH getDocumentHeaderByRefNoAndTenantCodeNewTran(String refNo, String tenantCode);
	TrDocumentH getEarliestAgreement(AmMsuser user);
	TrDocumentH getEarliestActiveAgreement(AmMsuser user);
	TrDocumentH getLatestUnsignedAgreement(AmMsuser user, MsTenant tenant, MsVendor vendor);
	
	List<TrDocumentH> getListDocumentHeaderByProsesMeterai(Short prosesMeterai);
	List<TrDocumentH> getListDocumentHeaderByProsesMeteraiNewTran(Short prosesMeterai);
	List<TrDocumentH> getListDocumentHeaderByCallbackProcess(Short callbackProcess);
	List<TrDocumentH> getListDocumentHeaderByCallbackProcessNewTrx(Short callbackProcess);
	List<TrDocumentH> getListDocumentHeaderByCallbackProcessAndDtmCrtNewTrx(Short callbackProcess, Date date);
	List<TrDocumentH> getListDocumentHeaderWithoutUserByCallbackProcessNewTrx(Short callbackProcess);
	List<TrDocumentH> getListDocumentHeaderByCallbackProcessAndIsManualUploadNewTrx(Short callbackProcess, String isManualUpload);

	List<DocHBean> getListDocumentBasedByCallbackValue(Integer max);

	void insertDocumentHeader(TrDocumentH documentH);
	void insertDocumentHeaderNewTrx(TrDocumentH documentH);
	
	void updateDocumentH(TrDocumentH documentH);
	void updateDocumentHNewTran(TrDocumentH documentH);
	void updateDocumentHNoRollBack(long idDocH, Date currDate, String usrUpd, short totalSigned);

	List<String> getListRefNumByLoginId(String loginId);
	List<DeleteOnPremResultBean> getDocToDeleteOnPremResult(Date maxDate, String limit);
	int countStampDutyNeeded(TrDocumentH documentH);
	int countDocToDeleteOnPremResult(Date maxDate);

//DOCUMENT D SIGN
	void insertDocumentDetailSign(TrDocumentDSign documentDSign);
	void updateDocumentDSign(TrDocumentDSign documentDSign);
	void updateDocumentDSignNoRollBack(long idDocDSign, Date currDate, String usrUpd);
	
	TrDocumentDSign getUnsignedDocumentDSign(TrDocumentD document);

	List<TrDocumentDSign> getDocumentDSignByIdDocumentDAndIdUser(Long idDocumentD, Long idUser);
	List<TrDocumentDSign> getDocumentDSignByIdDocumentDAndIdUserNewTrx(Long idDocumentD, Long idUser);
	List<TrDocumentDSign> getListDocumentDSignByIdDocumentD(long[] listIdDocumentD);
	
	int countSignedDocumentDSign(Long idDocumentD, Long idUser);

	List<Map<String, Object>> getDocumentSignerList(String documentId);

	boolean isUserSignedDocument(String documentId, Long idUser);

	List<SignerInfoBean> getListSignerSignStatusByIdDocumentD(long idDocumentD, long idMsVendor);

	BigInteger getDistinctSingerByIdDocumentDAndIdMsUser(Long[] listIdDocumentD, long idMsUser);
	
	List<SignerInfoBean> getListSignerSignStatusPrivyByIdDocumentD(long idDocumentD, long idMsVendor);

//DOCUMENT D STAMPDUTY
	void insertDocumentDetailSdt(TrDocumentDStampduty documentDSdt);

	void insertDocumentDetailSdtNewTrx(TrDocumentDStampduty documentDSdt);

	void updateDocumentDetailSdt(TrDocumentDStampduty documentDSdt);

	void updateDocumentDetailSdtNewTran(TrDocumentDStampduty documentDSdt);

	List<TrDocumentDStampduty> getDocumentStampDutyByIdDocumentD(Long idDocumentD);

	List<TrDocumentDStampduty> getDocumentStampDutyByIdDocumentDNewTran(Long idDocumentD);

//DOCUMENT H EMETERAI ERROR
	void insertDocumentHStampdutyError(TrDocumentHStampdutyError documentHEmeteraiError);

	void insertDocumentHStampdutyErrorNewTran(TrDocumentHStampdutyError documentHEmeteraiError);

	void updateDocumentHStampdutyError(TrDocumentHStampdutyError documentHEmeteraiError);

	void updateDocumentHStampdutyErrorNewTran(TrDocumentHStampdutyError documentHEmeteraiError);

	TrDocumentHStampdutyError getDocumentHStampdutyErrorByIdDocumentH(Long idDocumentH);

	TrDocumentHStampdutyError getDocumentHStampdutyErrorByIdDocumentHNewTran(Long idDocumentH);

	TrDocumentHStampdutyError getDocumentHStampdutyErrorNewTran(Long idDocumentH, Long idDocumentD);

	String getDocumentIdByPSREDocumentId(String psreDocument);

	List<TrDocumentHStampdutyError> getDocumentHStampdutyErrorsByIdDocumentH(Long idDocumentH);

	// MONITORING
	List<Map<String, Object>> getListMonitoring(int min, int max, String nomorDokumen, String tipeDokumen,
			String hasilStamping, String jenisDokumen, String templateDokumen, String noSN, String tenantCode,
			String taxType, String cabang, Date tanggalDokumenMulai, Date tanggalDokumenSampai);

	Integer countListMonitoring(int min, int max, String nomorDokumen, String tipeDokumen, String hasilStamping,
			String jenisDokumen, String templateDokumen, String noSN, String tenantCode, String taxType, String cabang,
			Date tanggalDokumenMulai, Date tanggalDokumenSampai);

	List<TaxReportBean> getListMonitoringReport(String nomorDokumen, String tipeDokumen, String hasilStamping,
			String jenisDokumen, String templateDokumen, String noSN, String tenantCode, String taxType, String cabang,
			Date tanggalDokumenMulai, Date tanggalDokumenSampai);

	// MONITORING EMBED
	List<Map<String, Object>> getListMonitoringEmbed(int min, int max, String nomorDokumen, String tipeDokumen,
			String hasilStamping, String jenisDokumen, String templateDokumen, String noSN, String tenantCode,
			Date tanggalDokumenMulai, Date tanggalDokumenSampai);

	Integer countListMonitoringEmbed(int min, int max, String nomorDokumen, String tipeDokumen, String hasilStamping,
			String jenisDokumen, String templateDokumen, String noSN, String tenantCode, Date tanggalDokumenMulai,
			Date tanggalDokumenSampai);

	List<TaxReportBean> getListMonitoringReportEmbed(String nomorDokumen, String tipeDokumen, String hasilStamping,
			String jenisDokumen, String templateDokumen, String noSN, String tenantCode, Date tanggalDokumenMulai,
			Date tanggalDokumenSampai);

	MsLov getLovSignStatusByPSREDocumentId(String psreDocument);

	List<Map<String, Object>> getSignerBeanByDocumentId(String documentId);

	String getDocumentTemplateCodeById(String idDocTemplate);

	String getDocumentIdByEmail(String signerEmail, String tenantCode);

	TrDocumentD getLatestDocumentDetailBySignerLoginIdAndIdMsVendor(String loginId, Long idMsVendor);

	List<TrDocumentHStampdutyError> getListDocumentHStampdutyErrorByIdDocumentH(Long idDocumentH);

	// SignBalance
	BigInteger getSignNeeded(String[] documentId, AmMsuser user);

	BigInteger getDocNeeded(String[] documentId, AmMsuser user);

	// Check Stamping Status
	List<Map<String, Object>> getDocIdByDocH(String refNumber, String tenantCode);

	// Report Download
	TrManualReport getDocReportById(long idManualReport);

	List<ListReportBean> getReportyByIdTenantPage(String tenantCode, int min, int max);

	public int countTrManualReport(GetListReportRequest request);

// TR DOCUMENT SIGNING REQUEST
	List<TrDocumentSigningRequest> getListTrDocumentSigningRequestByIdMsUserAndIdDocumentH(long idMsUser,
			long idDocumentH);

	// total unsigned doc
	BigInteger getTotalUnsignedDoc(long idMsTenant, long idMsUser);
	
//	TR PSRE SIGNING CONFIRMATION
	void insertPsreSigningConfirmation(TrPsreSigningConfirmation psreSigningConfirmation);

	void updatePsreSigningConfirmation(TrPsreSigningConfirmation psreSigningConfirmation);
	void deletePsreSigningConfirmation(TrPsreSigningConfirmation psreSigningConfirmation);
	TrPsreSigningConfirmation getTrPsreSigningConfirmationByIdMsUser(long idMsUser);
	TrPsreSigningConfirmation getTrPsreSigningConfirmationByIdMsUserAndDocumentId(long idMsUser, String documentId);

	void updateCallbackProcessNewTranNative(long idDocumentH, String usrUpd, short callbackProcess);
	void updateCallbackProcessAndRetryResumeAttemptNumNewTranNative(Long idDocumentH, String usrUpd, Short callbackProcess, Short retryResumeAttemptNumNewTranNative);

}
