package com.adins.esign.businesslogic.impl.interfacing;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.jaxrs.utils.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.interfacing.EmeteraiPajakkuLogic;
import com.adins.esign.confins.model.DocumentToUploadBean;
import com.adins.esign.confins.model.UploadToCoreBean;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.MediaType;
import com.adins.esign.constants.enums.StampingErrorDetail;
import com.adins.esign.constants.enums.StampingErrorLocation;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDStampduty;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrDocumentHStampdutyError;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.DataReturnBean;
import com.adins.esign.model.custom.DownloadStampedDocResponse;
import com.adins.esign.model.custom.EmailAttachmentBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.esign.webservices.model.DummyClientURLUploadRequest;
import com.adins.esign.webservices.model.GenerateEmeteraiPajakkuRequest;
import com.adins.esign.webservices.model.GenerateEmeteraiPajakkuResponse;
import com.adins.esign.webservices.model.StampingEmeteraiPajakkuRequest;
import com.adins.esign.webservices.model.StampingEmeteraiPajakkuResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.google.gson.Gson;

import okhttp3.Headers;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

import com.adins.esign.model.custom.EmeteraiPajakkuLoginRequestBean;
import com.adins.esign.model.custom.EmeteraiPajakkuLoginResponseBean;
import com.adins.esign.model.custom.PajakkuDocumentTypeBean;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.UploadDocPajakkuResponseBean;
import com.adins.esign.webservices.model.UpdateStampDutyStatusResponse;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.confins.UploadStampedDocumentRequest;
import com.adins.esign.webservices.model.confins.UploadStampedDocumentResponse;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.EmeteraiException.ReasonEmeterai;
import com.adins.exceptions.EmeteraiException;
import com.adins.framework.service.base.model.AuditDataType;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Transactional
@Component
public class GenericEmeteraiPajakkuLogic extends BaseLogic implements EmeteraiPajakkuLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericEmeteraiPajakkuLogic.class);
	
	private String headerAuth = HttpHeaders.AUTHORIZATION;
    private String headerContentType = HttpHeaders.CONTENT_TYPE;
    private String headerJson = MediaType.APPLICATION_JSON;
    private String headerPdf = MediaType.APPLICATION_PDF;
	private String contentTypeJson = MediaType.APPLICATION_JSON;
	
	private static final String BEARER = "Bearer ";
	
	private static final String PAJAKKU_SUCCESS_CODE = "00";
	private static final String PAJAKKU_ERROR_CODE = "01";
	
	public static final String DOC_TRX_ID_DELIMITER = ";";
	
	public static final long PERURI_CONNECTION_TIMEOUT_MILLIS = 30_000;
	public static final long PERURI_READ_TIMEOUT_MILLIS = 300_000;
	
	// Default data for attach E-Meterai error notification email
	private static final Integer MAX_ERROR_COUNT = 3;	
	private static final String[] ERROR_EMAIL_RECEIVER = {"<EMAIL>", "<EMAIL>", "<EMAIL>"};
	private static final Integer MAX_ERROR_MSG_LENGTH = 300;
	
	// Message di email notifikasi jika error tapi tidak ada tr_document_d
	private static final String MSG_EMPTY_DOCUMENT = "(document not specified)";
	
	@Value("${spring.mail.username}") private String fromEmailAddr;
	@Value("${emeterai.pajakku.generate.uri}") private String generateEmeteraiUrl;
	@Value("${emeterai.pajakku.namadoc}") private String namaDoc;
	@Value("${e-meterai.pajakku.user}") private String userPajakku;
	@Value("${e-meterai.pajakku.password}") private String passwordPajakku;
	@Value("${e-meterai.pajakku.login}") private String urlLogin;
	@Value("${e-meterai.pajakku.upload}") private String urlUploadDocument;
	@Value("${emeterai.pajakku.download}") private String urlDownload;
	@Value("${emeterai.pajakku.stamping.uri}") private String urlStamping;
	@Value("${emeterai.pajakku.certificatelevel}") private String certifLevel;
	@Value("${emeterai.pajakku.profilename}") private String profileName;
	@Value("${emeterai.pajakku.reason}") private String reason;
	@Value("${emeterai.pajakku.reason2}") private String reason2;
	@Value("${emeterai.pajakku.folder}") private String folder;
	@Value("${e-meterai.pajakku.processnumber}") private String processNumber;
	@Value("${emeterai.url.documenttype}") private String urlPajakkuDocumentType;
	@Value("${emeterai.uplcon.integrationvalue}") private String uplDmsIntegrationValue;

	@Autowired private Gson gson;
	
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private DocumentLogic documentLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private MessageTemplateLogic messageTemplateLogic;
	@Autowired private SaldoLogic saldoLogic;
	
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	
	private String getEmeteraiUsernameByAgreement(TrDocumentH documentH) {
		if (!"1".equals(documentH.getIsPostpaidStampduty())) {
			return userPajakku;
		}
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCodeAndTenant(AmGlobalKey.GENERALSETTING_PAJAKKU_USERNAME, documentH.getMsTenant());
		if (null == gs) {
			return null;
		}
		return gs.getGsValue();
	}
	
	private String getEmeteraiPasswordByAgreement(TrDocumentH documentH) {
		if (!"1".equals(documentH.getIsPostpaidStampduty())) {
			return passwordPajakku;
		}
		
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCodeAndTenant(AmGlobalKey.GENERALSETTING_PAJAKKU_PASSWORD, documentH.getMsTenant());
		if (null == gs) {
			return null;
		}
		return gs.getGsValue();
	}
	
	private String getNamaDocForGenerateEmeterai(TrDocumentD document) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return document.getMsPeruriDocType().getDocName();
		}
		return namaDoc;
	}
	
	private String getNoDocForGenerateEmeterai(TrDocumentD document) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return document.getTrDocumentH().getRefNumber();
		}
		return document.getDocumentId();
	}
	
	private String getTglDocForGenerateEmeterai(TrDocumentD document) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return MssTool.formatDateToStringIn(document.getRequestDate(), GlobalVal.DATE_FORMAT);
		}
		return MssTool.formatDateToStringIn(document.getCompletedDate(), GlobalVal.DATE_FORMAT);
	}
	
	private String getBalanceMutationNotesForGenerateEmeterai(TrDocumentD document, TrStampDuty stampDuty, int currentLoop) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			List<TrDocumentDStampduty> sdtLocs =  daoFactory.getDocumentDao().getDocumentStampDutyByIdDocumentD(document.getIdDocumentD());
			return StringUtils.isNotBlank(sdtLocs.get(currentLoop).getNotes()) ? sdtLocs.get(currentLoop).getNotes() : stampDuty.getStampDutyNo();
		}
		return stampDuty.getStampDutyNo();
	}
	
	private String getReasonForStampingEmeterai(TrDocumentD document) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload())) {
			return reason2;
		}
		return reason;
	}
	
	private String buildGenerateErrorMessage(GenerateEmeteraiPajakkuResponse response) {
		StringBuilder message = new StringBuilder();
		message.append(response.getMessage());
		if (response.getResult().getErr() instanceof String) {
			message.append(" ").append(response.getResult().getErr());
		}
		
		return message.toString();
	}
	
	private long getGeneralSettingConnectionTimeouMillisValue(AuditContext audit) {
		String timeoutValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_PERURI_CONN_TIMEOUT, audit);
		if (StringUtils.isBlank(timeoutValue)) {
			return PERURI_CONNECTION_TIMEOUT_MILLIS; 
		}
		
		try {
			return Long.parseLong(timeoutValue);
		} catch (Exception e) {
			return PERURI_CONNECTION_TIMEOUT_MILLIS;
		}
	}
	
	private long getGeneralSettingReadTimeouMillisValue(AuditContext audit) {
		String timeoutValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_PERURI_READ_TIMEOUT, audit);
		if (StringUtils.isBlank(timeoutValue)) {
			return PERURI_READ_TIMEOUT_MILLIS; 
		}
		
		try {
			return Long.parseLong(timeoutValue);
		} catch (Exception e) {
			return PERURI_READ_TIMEOUT_MILLIS;
		}
	}
	
	private Integer getMaxErrorCount(AuditContext audit) {
		String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_ATTACH_SDT_MAX_REPEATED_ERROR_COUNT, audit);
		if (StringUtils.isBlank(gsValue)) {
			return MAX_ERROR_COUNT;
		}
		
		try {
			return Integer.valueOf(gsValue);
		} catch (Exception e) {
			return MAX_ERROR_COUNT;
		}
	}
	
	private String[] getErrorEmailRecipients(MsTenant tenant) {
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ATTACH_SDT_ERROR_EMAIL_RECEIVER);
		if (null == tenantSettings) {
			return ERROR_EMAIL_RECEIVER;
		}
		
		String emailReceivers = tenantSettings.getSettingValue();
		if (StringUtils.isBlank(emailReceivers)) {
			return ERROR_EMAIL_RECEIVER;
		}
		
		String[] receivers = emailReceivers.split(";");
		if (StringUtils.isBlank(receivers[0])) {
			receivers = ERROR_EMAIL_RECEIVER;
		}
		return receivers;
	}
	
	private String getManuallyUploadedDocumentFromOss(TrDocumentD document, AuditContext audit) {
		String tenantCode = document.getMsTenant().getTenantCode();
		String refNumber = document.getTrDocumentH().getRefNumber();
		String documentId = document.getDocumentId();
		byte[] documentByteArray =  cloudStorageLogic.getStampingDocument(tenantCode, refNumber, documentId);
		if (null == documentByteArray) {
			throw new EmeteraiException(getMessage("businesslogic.emeterai.failedtogetossdocument",
					new String[] {document.getDocumentId()}, audit), ReasonEmeterai.DOWNLOAD_DOC_EXCEPTION);
		}
		
		return Base64.getEncoder().encodeToString(documentByteArray);
	}
	
	private void uploadStampedDocumentToOss(TrDocumentD document, String loginToken, AuditContext audit) {
		
		LOG.info("Kontrak {}, Dokumen {}, Uploading stamped document to OSS", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
		
		DownloadStampedDocResponse downloadResponse = downloadStampedDoc(document, loginToken, audit);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(downloadResponse.getResponseCode())) {
			throw new EmeteraiException(downloadResponse.getMessage(), ReasonEmeterai.DOWNLOAD_DOC_RESPONSE_ERROR);
		}
		
		String base64Pdf = downloadResponse.getDataReturn().getPdfFile();
		byte[] documentByteArray = Base64.getDecoder().decode(base64Pdf);
		cloudStorageLogic.storeStampedDocument(document, documentByteArray);
	}
	
	private String getStampedDocument(TrDocumentD document, String peruriLoginToken, AuditContext audit) {
		// Get document from OSS
		byte[] documentByteArray = cloudStorageLogic.getStampedDocument(document);
		if (null != documentByteArray) {
			return Base64.getEncoder().encodeToString(documentByteArray);
		}
		
		// If document not found in OSS, download from Peruri
		DownloadStampedDocResponse downloadResponse = downloadStampedDoc(document, peruriLoginToken, audit);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(downloadResponse.getResponseCode())) {
			throw new EmeteraiException(downloadResponse.getMessage(), ReasonEmeterai.DOWNLOAD_DOC_RESPONSE_ERROR);
		}
		
		return downloadResponse.getDataReturn().getPdfFile();
	}
	
	private boolean allDocumentsProcessed(List<TrDocumentD> documents) {
		if (CollectionUtils.isEmpty(documents)) {
			return true;
		}
		for (TrDocumentD document : documents) {
			LOG.info("FINAL CHECK: Kontrak {}, Document {}, Total stamped {} / {}, Current process: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), document.getTotalStamping(), document.getTotalMaterai(), document.getSdtProcess());
			if (!document.getTotalMaterai().equals(document.getTotalStamping())) {
				return false;
			}
			if (!GlobalVal.STEP_ATTACH_METERAI_SDT_FIN.equals(document.getSdtProcess())) {
				return false;
			}
		}
		return true;
	}
	
	@Override
	public UpdateStampDutyStatusResponse attachMeteraiPajakku(AuditContext audit) {
		Short process = Short.valueOf(processNumber);
		List<TrDocumentH> listDocumentH = daoFactory.getDocumentDao().getListDocumentHeaderByProsesMeteraiNewTran(process);
		for (TrDocumentH documentH : listDocumentH) {
			attachMeteraiPajakkuPerContract(documentH, audit);
		}
		return new UpdateStampDutyStatusResponse();
	}
	
	private UpdateStampDutyStatusResponse attachMeteraiPajakkuPerContract(TrDocumentH documentH, AuditContext audit) {
		updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_PROCESS, null, audit);
		
		long connTimeoutMillis = getGeneralSettingConnectionTimeouMillisValue(audit);
		long readTimeoutMillis = getGeneralSettingReadTimeouMillisValue(audit);
		
		EmeteraiPajakkuLoginResponseBean loginResponse = loginPajakku(documentH, false, connTimeoutMillis, readTimeoutMillis, audit);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(loginResponse.getStatusCode())) {
			return new UpdateStampDutyStatusResponse();
		}
		
		List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
		
		for (TrDocumentD document: documents) {
			LOG.info("Kontrak {}, Dokumen {}, Total Meterai: {}, Total Stamping: {}", documentH.getRefNumber(), document.getDocumentId(), document.getTotalMaterai(), document.getTotalStamping());
			
			if (null == document.getTotalMaterai() || 0 == document.getTotalMaterai()) {
				updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_NOT_SDT, audit);
				continue;
			}
			
			String loginToken = loginResponse.getToken();
			
			int currentLoop = document.getTotalStamping();
			for (int i = currentLoop; i < document.getTotalMaterai(); i++) {
				if (GlobalVal.STEP_ATTACH_METERAI_NOT_STR.equals(document.getSdtProcess())) {
					updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
				}
				if (GlobalVal.STEP_ATTACH_METERAI_UPL_DOC.equals(document.getSdtProcess())) {
					UploadDocPajakkuResponseBean response = uploadDocPajakku(document, loginToken, connTimeoutMillis, readTimeoutMillis, false, audit);
					if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
						return new UpdateStampDutyStatusResponse();
					}
					updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_GEN_SDT, audit);
				}
				if (GlobalVal.STEP_ATTACH_METERAI_GEN_SDT.equals(document.getSdtProcess())) {
					GenerateEmeteraiPajakkuResponse response = generateEmeterai(document, i, loginToken, connTimeoutMillis, readTimeoutMillis, false, audit);
					if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
						return new UpdateStampDutyStatusResponse();
					}
					updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
				}
				if (GlobalVal.STEP_ATTACH_METERAI_STM_SDT.equals(document.getSdtProcess())) {
					StampingEmeteraiPajakkuResponse response = stampingEmeterai(document, i, loginToken, connTimeoutMillis, readTimeoutMillis, false, audit);
					if (!GlobalVal.PERURI_STAMPING_SUCCESS_STATUS.equalsIgnoreCase(response.getStatus())) {
						return new UpdateStampDutyStatusResponse();
					}
					if (document.getTotalMaterai().equals(document.getTotalStamping())) {
						updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_UPL_CON, audit);
					} else {
						updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
					}
				}
				LOG.info("Kontrak {}, Dokumen {}, Total stamped: {}/{}", documentH.getRefNumber(), document.getDocumentId(), document.getTotalStamping(), document.getTotalMaterai());
			}
			
			if (GlobalVal.STEP_ATTACH_METERAI_UPL_CON.equals(document.getSdtProcess())) {
				Status status = uploadStampedDocument(document, loginToken, false, audit);
				if (200 != status.getCode()) {
					return new UpdateStampDutyStatusResponse();
				}
				updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_SDT_FIN, audit);
			}
		}
		
		if (allDocumentsProcessed(documents)) {
			updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_FINISHED, null, audit);
			return new UpdateStampDutyStatusResponse();
		}
		
		String message = "Should not reach this process. Please check the data";
		updateStatusProcessMeterai(documentH, processNumber, null, audit);
		checkAgreementErrorCount(documentH, null, StampingErrorLocation.FINAL_VAL, StampingErrorDetail.VALIDATION, message, null, null, null, audit);
		
		LOG.warn("Kontrak {} should not reach this process", documentH.getRefNumber());
		return new UpdateStampDutyStatusResponse();
	}
	
	private EmeteraiPajakkuLoginResponseBean loginPajakku(TrDocumentH documentH, boolean throwException, long connTimeoutMillis, long readTimeoutMillis, AuditContext audit) {
		String jsonRequest = null;
		String jsonResponse = null;
		
		try {
			String username = getEmeteraiUsernameByAgreement(documentH);
			String password = getEmeteraiPasswordByAgreement(documentH);
			
			if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
				String message = getMessage("businesslogic.emeterai.emptylogincredential", null, audit);
				throw new EmeteraiException(message, ReasonEmeterai.INVALID_CREDENTIAL);
			}
			
			// Prepare header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(headerContentType, headerJson);
			WebClient client = WebClient.create(urlLogin).headers(mapHeader);
			MssTool.setWebClientConnReadTimeout(client, connTimeoutMillis, readTimeoutMillis);
			
			// Prepare request body
			EmeteraiPajakkuLoginRequestBean request = new EmeteraiPajakkuLoginRequestBean();
			request.setUser(username);
			request.setPassword(password);
			jsonRequest = gson.toJson(request);
			LOG.info("Kontrak {}, Login Pajakku request: {}", documentH.getRefNumber(), jsonRequest);
			
			// Get response
			Response clientResponse = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) clientResponse.getEntity());
			jsonResponse = IOUtils.toString(isReader);
			LOG.info("Kontrak {}, Login Pajakku response: {}", documentH.getRefNumber(), jsonResponse);
			
			EmeteraiPajakkuLoginResponseBean response = gson.fromJson(jsonResponse, EmeteraiPajakkuLoginResponseBean.class);
			response.setJsonRequest(jsonRequest);
			response.setJsonResponse(jsonResponse);
			
			if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
				
				String message = null;
				if (response.getResult() instanceof String) {
					message = (String) response.getResult();
				}
				
				updateStatusProcessMeterai(documentH, processNumber, null, audit);
				checkAgreementErrorCount(documentH, null, StampingErrorLocation.LOGIN, StampingErrorDetail.FAIL_RESPONSE, message, null, jsonRequest, jsonResponse, audit);
			}
			
			return response;
		} catch (Exception e) {
			
			updateStatusProcessMeterai(documentH, processNumber, null, audit);
			checkAgreementErrorCount(documentH, null, StampingErrorLocation.LOGIN, StampingErrorDetail.EXCEPTION, e.getLocalizedMessage(), e, jsonRequest, jsonResponse, audit);
			
			if (throwException) {
				throw new EmeteraiException(e.getLocalizedMessage(), e, ReasonEmeterai.LOGIN_EXCEPTION);
			}
			
			EmeteraiPajakkuLoginResponseBean response = new EmeteraiPajakkuLoginResponseBean();
			response.setStatusCode(PAJAKKU_ERROR_CODE);
			response.setErrorMsg(e.getLocalizedMessage());
			response.setErrorMessage(e.getLocalizedMessage());
			response.setException(e);
			response.setJsonRequest(jsonRequest);
			response.setJsonResponse(jsonResponse);
			return response;
		}
		
	}
	
	private UploadDocPajakkuResponseBean uploadDocPajakku(TrDocumentD document, 
			String tokenPajakku, long connTimeoutMillis, long readTimeoutMillis,
			boolean throwException, AuditContext audit) {
		
		String jsonResponse = null;
		try {
			
			UploadDocPajakkuResponseBean response = new UploadDocPajakkuResponseBean();
			
			if (StringUtils.isNotBlank(document.getTransactionId()) && document.getTransactionId().split(DOC_TRX_ID_DELIMITER).length >= document.getTotalMaterai()) {
				response.setStatusCode(GlobalVal.PERURI_SUCCESS_CODE);
				return response;
			}
			
			String pdfBase64 = getDocumentFileToUpload(document, tokenPajakku, audit);
			if (StringUtils.isBlank(pdfBase64)) {
				String message = "Empty document file";
				
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.UPL_DOC, StampingErrorDetail.VALIDATION, message, null, null, null, audit);
				
				response.setErrorCode(PAJAKKU_ERROR_CODE);
				response.setErrorMsg(message);
				return response;
			}
			
			byte[] pdfFile = Base64.getDecoder().decode(pdfBase64);
			String fileName = document.getDocumentId() + ".pdf";
				
			OkHttpClient client = new OkHttpClient.Builder()
					.connectTimeout(connTimeoutMillis, TimeUnit.MILLISECONDS)
					.writeTimeout(15L, TimeUnit.SECONDS)
					.readTimeout(readTimeoutMillis, TimeUnit.MILLISECONDS)
					.build();
				
			RequestBody body = new MultipartBody.Builder()
					.setType(MultipartBody.FORM)
					.addFormDataPart("file", fileName, RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_PDF), pdfFile))
					.addFormDataPart("token", tokenPajakku)
					.build();
				
			Request request = new Request.Builder()
					.url(urlUploadDocument)
					.addHeader(headerAuth, BEARER + tokenPajakku)
					.post(body).build();
			
			LOG.info("Kontrak {}, Dokumen {}, Upload document to Peruri with filename: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), fileName);
			
			okhttp3.Response responseClient = client.newCall(request).execute();
			String result = responseClient.body().string();
			jsonResponse = result;
			LOG.info("Kontrak {}, Dokumen {}, Upload document to Peruri response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), result);
			
			response = gson.fromJson(result, UploadDocPajakkuResponseBean.class);
			
			if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.UPL_DOC, StampingErrorDetail.FAIL_RESPONSE, response.getMessage(), null, null, jsonResponse, audit);
				
				response.setErrorMessage(response.getMessage());
				response.setJsonResponse(result);
				return response;
			}
			
			String updatedTransactionId;
			if (StringUtils.isNotBlank(document.getTransactionId())) {
				updatedTransactionId = document.getTransactionId() + ";" + response.getId();
			} else {
				updatedTransactionId = response.getId();
			}
			LOG.info("Kontrak {}, Dokumen {}, Updated Transaction Id: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), updatedTransactionId);
			
			String updatedDocumentNameSdt;
			if (StringUtils.isNotBlank(document.getDocumentNameSdt())) {
				updatedDocumentNameSdt = document.getDocumentNameSdt() + ";" + response.getSaveAs();
			} else {
				updatedDocumentNameSdt = response.getSaveAs();
			}
			LOG.info("Kontrak {}, Dokumen {}, Updated Document Name Sdt: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), updatedDocumentNameSdt);
					
			document.setUsrUpd(audit.getCallerId());
			document.setDtmUpd(new Date());
			document.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_GEN_SDT);
			document.setTransactionId(updatedTransactionId);
			document.setDocumentNameSdt(updatedDocumentNameSdt);
			daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
			
			return response;
		} catch (Exception e) {
			
			LOG.error("Kontrak {}, Dokumen {}, Upload document to Peruri exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage());
			
			updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
			checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.UPL_DOC, StampingErrorDetail.EXCEPTION, e.getLocalizedMessage(), e, null, jsonResponse, audit);
			
			if (throwException) {
				throw new EmeteraiException(e.getLocalizedMessage(), e, ReasonEmeterai.UPLOAD_DOC_EXCEPTION);
			}
			
			UploadDocPajakkuResponseBean response = new UploadDocPajakkuResponseBean();
			response.setStatusCode(PAJAKKU_ERROR_CODE);
			response.setErrorMessage(response.getMessage());
			response.setJsonResponse(jsonResponse);
			return response;
		}
	}
	
	private String updateStatusProcessMeterai(TrDocumentH documentH, String statusAttachMeterai, Exception e, AuditContext audit) {
		if (null != e) {
			LOG.error(e.getLocalizedMessage(), e);
		}

		documentH.setProsesMaterai(new Short(statusAttachMeterai));
		documentH.setDtmUpd(new Date());
		documentH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
		
		LOG.info("Kontrak {} update Proses Meterai to {}", documentH.getRefNumber(), statusAttachMeterai);

		return statusAttachMeterai;
	}
	
	private String updateStepAttachMeterai(TrDocumentD documentD, String step, AuditContext audit) {
		documentD.setDtmUpd(new Date());
		documentD.setUsrUpd(audit.getCallerId());
		documentD.setSdtProcess(step);
		daoFactory.getDocumentDao().updateDocumentDetailNewTran(documentD);
		return step;
	}
	
	private GenerateEmeteraiPajakkuResponse generateEmeterai(TrDocumentD document, int currentLoop,
			String token, long connTimeoutMillis, long readTimeoutMillis, boolean throwException, AuditContext audit) {
		try {
			GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
			
			if (StringUtils.isBlank(document.getTransactionId())) {
				LOG.warn("Kontrak {}, Dokumen {}: transaction_id is empty.", document.getTrDocumentH().getRefNumber(), document.getDocumentId());		
				String message = "Transaction id empty";
				
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.GEN_SDT, StampingErrorDetail.VALIDATION, message, null, null, null, audit);
				
				response.setMessage(message);
				response.setStatusCode(PAJAKKU_ERROR_CODE);
				response.setErrorMsg(message);
				return response;
			}
			
			int sdtNeeded = document.getTotalMaterai() - document.getTotalStamping();
			int availableSdt = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
			
			LOG.info("Kontrak {}, Document {}, Need SDT: {}, Available SDT: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtNeeded, availableSdt);
			if (availableSdt == sdtNeeded) {
				response.setMessage("Stamp duty already available");
				response.setStatusCode(PAJAKKU_SUCCESS_CODE);
				return response;
			}
			
			if (!enoughSdtBalance(document.getTrDocumentH(), document, sdtNeeded, audit)) {
				String message = "Not enough SDT balance";
				
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.GEN_SDT, StampingErrorDetail.VALIDATION, message, null, null, null, audit);
				
				response.setMessage(message);
				response.setStatusCode(PAJAKKU_ERROR_CODE);
				response.setErrorMsg(message);
				return response;
			}
			
			String[] transactionIds = document.getTransactionId().split(DOC_TRX_ID_DELIMITER);
			String[] documentNames = document.getDocumentNameSdt().split(DOC_TRX_ID_DELIMITER);
			
			String nilaiMeteraiLunas = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_NILAI_METERAI_LUNAS, audit);
			if (StringUtils.isBlank(nilaiMeteraiLunas)) {
				nilaiMeteraiLunas = GlobalVal.PAJAKKU_NILAI_METERAI_LUNAS;
			}
			
			GenerateEmeteraiPajakkuRequest request = new GenerateEmeteraiPajakkuRequest();
			request.setIdfile(transactionIds[currentLoop]);
			request.setUpload(true);
			request.setNamadoc(getNamaDocForGenerateEmeterai(document));
			request.setNamafile(documentNames[currentLoop]);
			request.setNilaidoc(nilaiMeteraiLunas);
			request.setSnOnly(false);
			request.setNodoc(getNoDocForGenerateEmeterai(document));
			request.setTgldoc(getTglDocForGenerateEmeterai(document));
			
			if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && "1".equals(document.getTrDocumentH().getIsPostpaidStampduty()) && StringUtils.isNotBlank(document.getDocumentName())) {
				request.setNamejidentitas(document.getMsLovIdType().getCode());
				request.setNoidentitas(document.getIdNo());
				request.setNamedipungut(document.getIdName());
			}
			
			response = generateEmeteraiPajakku(request, token, document, connTimeoutMillis, readTimeoutMillis);
			if (!PAJAKKU_SUCCESS_CODE.equals(response.getStatusCode())) {
				String message = buildGenerateErrorMessage(response);
				String jsonRequest = response.getJsonRequest();
				String jsonResponse = response.getJsonResponse();
				
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.GEN_SDT, StampingErrorDetail.FAIL_RESPONSE, message, null, jsonRequest, jsonResponse, audit);
				return response;
			}
			
			MsTenant tenant = document.getMsTenant();
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
			
			String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_AVAILABLE);
			MsLov balanceType = null;
			MsLov trxType = null;
			if ("1".equals(document.getTrDocumentH().getIsPostpaidStampduty())) {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT_POSTPAID);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT_POSTPAID);
			} else {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT);
			}
			
			TrStampDuty sdt = new TrStampDuty();
			sdt.setTrxNo(trxNo);
			sdt.setStampDutyNo(response.getResult().getSn());
			sdt.setStampQr(response.getResult().getFilenameQR());
			sdt.setMsLov(sdtStatus);
			sdt.setUsrCrt(audit.getCallerId());
			sdt.setDtmCrt(new Date());
			sdt.setMsTenant(tenant);
			sdt.setMsVendor(vendor);
			sdt.setStampDutyFee(Integer.valueOf(nilaiMeteraiLunas));
			daoFactory.getStampDutyDao().insertTrStampDutyNewTran(sdt);
			
			LOG.info("Kontrak {}, stamp duty with SN {} inserted.", document.getTrDocumentH().getRefNumber(), sdt.getStampDutyNo());
			
			String notes = getBalanceMutationNotesForGenerateEmeterai(document, sdt, currentLoop);
			
			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(trxNo);
			mutation.setTrxDate(new Date());
			mutation.setRefNo(document.getTrDocumentH().getRefNumber());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setTrDocumentD(document);
			mutation.setTrDocumentH(document.getTrDocumentH());
			mutation.setNotes(notes);
			mutation.setTrStampDuty(sdt);
			mutation.setUsrCrt(audit.getCallerId());
			mutation.setDtmCrt(new Date());
			mutation.setMsOffice(document.getTrDocumentH().getMsOffice());
			mutation.setMsBusinessLine(document.getTrDocumentH().getMsBusinessLine());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
			
			LOG.info("Kontrak {}, balance mutation with stamp duty SN {} inserted.", document.getTrDocumentH().getRefNumber(), sdt.getStampDutyNo());
			
			updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
			
			return response;
		} catch (Exception e) {
			
			LOG.error("Generate Emeterai error: {}", e.getLocalizedMessage(), e);
			
			updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
			checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.GEN_SDT, StampingErrorDetail.EXCEPTION, e.getLocalizedMessage(), e, null, null, audit);
			
			if (throwException) {
				throw new EmeteraiException(e.getLocalizedMessage(), e, ReasonEmeterai.GENERATE_EXCEPTION);
			}
			GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
			response.setMessage(e.getLocalizedMessage());
			response.setStatusCode(PAJAKKU_ERROR_CODE);
			response.setErrorMsg(e.getLocalizedMessage());
			return response;
		}
	}
	
	private boolean enoughSdtBalance(TrDocumentH documentH, TrDocumentD document, int sdtNeeded, AuditContext audit) {
		
		if ("1".equals(documentH.getIsPostpaidStampduty())) {
			return true;
		}
		
		MsTenant tenant = document.getMsTenant();
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		
		BalanceRequest balanceRequest = new BalanceRequest();
		balanceRequest.setTenantCode(tenant.getTenantCode());
		balanceRequest.setVendorCode(GlobalVal.VENDOR_CODE_ESG);
		balanceRequest.setBalanceType(GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		
		Integer balance = saldoLogic.getBalanceNotSecure(balanceRequest, audit).getListBalance().get(0).getCurrentBalance().intValue();
		if (balance < sdtNeeded) {
			
			if (null != documentH.getEmailSaldo() && documentH.getEmailSaldo().intValue() >= 1) {
				LOG.info("Insufficient balance email has already been sent for Nomor Kontrak {}", documentH.getRefNumber());
				return false;
			}
			
			if (null == documentH.getEmailSaldo() || 0 == documentH.getEmailSaldo().intValue()) {
				documentH.setEmailSaldo((short) 1);
				documentH.setUsrUpd(audit.getCallerId());
				documentH.setDtmUpd(new Date());
				daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
			}
			
			LOG.warn("Insufficient balance to generate Emeterai for tenant {}, Kontrak {}", tenant.getTenantCode(), documentH.getRefNumber());
			String[] recipient = tenant.getEmailReminderDest().split(",");
			
			sendInsufficientSdtBalanceEmail("generate e-Meterai", documentH.getRefNumber(), document.getMsDocTemplate().getDocTemplateName(),
					balanceType.getDescription(), balance, recipient);
			
			return false;
		}
		return true;
	}
	
	private void sendInsufficientSdtBalanceEmail(String action, String refNo, String documentName, String balanceType, Integer saldo, String[] emailDest) {
		Map<String, Object> reminder = new HashMap<>();
		reminder.put("title", action);
		reminder.put("action", action);
		reminder.put("refNo", refNo);
		reminder.put("documentName", documentName);
		reminder.put("balanceType", balanceType);
		reminder.put("saldo", saldo);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("reminder", reminder);

		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INSUFFICIENT_BAL, templateParameters);
		String[] recipient = emailDest;

		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setFrom(fromEmailAddr);
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());

		try {
			emailSenderLogic.sendEmail(emailBean, null);
		} catch (Exception e) {
			LOG.error("Kontrak {}, Send email error: {}", refNo, e.getMessage());
		}
	}

	private GenerateEmeteraiPajakkuResponse generateEmeteraiPajakku(GenerateEmeteraiPajakkuRequest request, String token, 
			TrDocumentD document, long connTimeoutMillis, long readTimeoutMillis) throws IOException {
	
		String url = generateEmeteraiUrl;
		String jsonRequest = gson.toJson(request);
		LOG.info("Kontrak {}, Dokumen {}, Generate Emeterai request: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonRequest);
			
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerAuth, BEARER + token);
		mapHeader.add(headerContentType, contentTypeJson);
		WebClient client = WebClient.create(url).headers(mapHeader);
		MssTool.setWebClientConnReadTimeout(client, connTimeoutMillis, readTimeoutMillis);
			
		Response response = client.post(jsonRequest);
			
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String generateResult = IOUtils.toString(isReader);
					
		LOG.info("Kontrak {}, Dokumen {}, Generate Emeterai response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), generateResult);
		GenerateEmeteraiPajakkuResponse generateResponse = gson.fromJson(generateResult, GenerateEmeteraiPajakkuResponse.class);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(generateResponse.getStatusCode())) {
			generateResponse.setErrorMessage(generateResponse.getMessage());
			generateResponse.setErrorMsg(generateResponse.getMessage());
			generateResponse.setJsonRequest(jsonRequest);
			generateResponse.setJsonResponse(generateResult);
		}
		return generateResponse;
	}
	
	private StampingEmeteraiPajakkuResponse stampingEmeterai(TrDocumentD document, int currentLoop,
			String token, long connTimeoutMillis, long readTimeoutMillis,
			boolean throwException, AuditContext audit) {
		try {
			StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
			
			int sdtAvailable = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
			LOG.info("Kontrak {}, Dokumen {}, available SDT qty: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtAvailable);
			if (0 == sdtAvailable) {
				String message = "No stamp duty for " + document.getDocumentId();
				
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.STM_SDT, StampingErrorDetail.VALIDATION, message, null, null, null, audit);
				
				response.setErrorCode(PAJAKKU_ERROR_CODE);
				response.setErrorMessage(message);
				response.setErrorMsg(message);
				return response;
			}
			
			String[] transactionIds = document.getTransactionId().split(DOC_TRX_ID_DELIMITER);
			String[] documentNames = document.getDocumentNameSdt().split(DOC_TRX_ID_DELIMITER);
			
			List<Map<String, Object>> stampDutyIds = daoFactory.getStampDutyDao().getIdStampDutyForDocumentNewTran(document);
			BigInteger idStampDuty = (BigInteger) stampDutyIds.get(currentLoop).get("d0");
			TrStampDuty sdt = daoFactory.getStampDutyDao().getStampDutyByIdNewTran(idStampDuty.longValue());
			if (!GlobalVal.CODE_LOV_SDT_AVAILABLE.equals(sdt.getMsLov().getCode())) {
				String message = "SDT with number " + sdt.getStampDutyNo() + " is not available.";
				
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.STM_SDT, StampingErrorDetail.VALIDATION, message, null, null, null, audit);
				
				response.setErrorCode(PAJAKKU_ERROR_CODE);
				response.setErrorMessage(message);
				response.setErrorMsg(message);
				return response;
			}
			
			List<TrDocumentDStampduty> sdtLocs = daoFactory.getDocumentDao().getDocumentStampDutyByIdDocumentDNewTran(document.getIdDocumentD());
			TrDocumentDStampduty sdtLoc = sdtLocs.get(currentLoop);
			SignLocationBean coordinate = gson.fromJson(sdtLoc.getSignLocation(), SignLocationBean.class);
			
			StampingEmeteraiPajakkuRequest request = new StampingEmeteraiPajakkuRequest();
			request.setOnPrem(false);
			request.setDocId(transactionIds[currentLoop]);
			request.setCertificatelevel(certifLevel);
			request.setDest(folder + "final_" + transactionIds[currentLoop] + ".pdf");
			request.setDocpass(StringUtils.EMPTY);
			request.setJwToken(token);
			request.setLocation(document.getTrDocumentH().getMsOffice().getOfficeName());
			request.setProfileName(profileName);
			request.setReason(getReasonForStampingEmeterai(document));
			request.setRefToken(sdt.getStampDutyNo());
			request.setSpesimenPath(folder + sdt.getStampQr());
			request.setSrc(folder + documentNames[currentLoop]);
			request.setRetryFlag("1");
			request.setVisLLX(Double.valueOf(coordinate.getLlx()));
			request.setVisLLY(Double.valueOf(coordinate.getLly()));
			request.setVisURX(Double.valueOf(coordinate.getUrx()));
			request.setVisURY(Double.valueOf(coordinate.getUry()));
			request.setVisSignaturePage(sdtLoc.getSignPage());
			
			response = stampingEmeteraiPajakku(request, token,
					document.getTrDocumentH().getRefNumber(), document.getDocumentId(), 
					connTimeoutMillis, readTimeoutMillis);
			if (!PAJAKKU_SUCCESS_CODE.equals(response.getErrorCode())) {
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.STM_SDT, StampingErrorDetail.FAIL_RESPONSE, response.getErrorMessage(), null, response.getJsonRequest(), response.getJsonResponse(), audit);
				return response;
			}
			
			// Update SDT Status to GO LIVE
			MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_GO_LIVE);
			sdt.setMsLov(sdtStatus);
			sdt.setUsrUpd(audit.getCallerId());
			sdt.setDtmUpd(new Date());
			daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
			
			// Update tr_document_d_stampduty, fill id_stamp_duty and stamp date
			sdtLoc.setTrStampDuty(sdt);
			sdtLoc.setStampingDate(new Date());
			sdtLoc.setUsrUpd(audit.getCallerId());
			sdtLoc.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(sdtLoc);
			
			// Update documentD
			short stamped = document.getTotalStamping();
			stamped += 1;
			document.setTotalStamping(stamped);
			
			String currentLink = StringUtils.EMPTY;
			if (StringUtils.isNotBlank(document.getDocumentSdtLink())) {
				currentLink += document.getDocumentSdtLink() + DOC_TRX_ID_DELIMITER;
			}
			currentLink += response.getUrlFile();
			document.setDocumentSdtLink(currentLink);
			daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
			
			uploadStampedDocumentToOss(document, token, audit);
			return response;
		} catch (Exception e) {
			
			LOG.info("Kontrak {}, Dokumen {}, Stamping Emeterai exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage());
			
			updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
			checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.STM_SDT, StampingErrorDetail.EXCEPTION, e.getLocalizedMessage(), e, null, null, audit);
			
			if (throwException) {
				throw new EmeteraiException(e.getLocalizedMessage(), e, ReasonEmeterai.STAMPING_EXCEPTION);
			}
			StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
			response.setErrorCode(PAJAKKU_ERROR_CODE);
			response.setErrorMessage(e.getLocalizedMessage());
			response.setErrorMsg(e.getLocalizedMessage());
			response.setException(e);
			return response;
		}
		
	}
	
	private StampingEmeteraiPajakkuResponse stampingEmeteraiPajakku(StampingEmeteraiPajakkuRequest request, String token,
			String refNumber, String documentId,
			long connTimeoutMillis, long readTimeoutMillis) throws IOException {
		
		String url = urlStamping;
		String jsonRequest = gson.toJson(request);
		LOG.info("Kontrak {}, Dokumen {}, Stamping Emeterai request: {}", refNumber, documentId, jsonRequest);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerAuth, BEARER + token);
		mapHeader.add(headerContentType, contentTypeJson);
		WebClient client = WebClient.create(url).headers(mapHeader);
		MssTool.setWebClientConnReadTimeout(client, connTimeoutMillis, readTimeoutMillis);
		
		Response response = client.post(jsonRequest);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String result = IOUtils.toString(isReader);
		
		LOG.info("Kontrak {}, Dokumen {}, Stamping Emeterai response: {}", refNumber, documentId, result);
		StampingEmeteraiPajakkuResponse stampingResponse = gson.fromJson(result, StampingEmeteraiPajakkuResponse.class);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(stampingResponse.getErrorCode())) {
			stampingResponse.setErrorMsg(stampingResponse.getErrorMessage());
			stampingResponse.setJsonRequest(jsonRequest);
			stampingResponse.setJsonResponse(result);
		}
		
		return stampingResponse;
	}
	
	private DummyClientURLUploadRequest prepareUploadToClientRequest(TrDocumentD document, AuditContext audit) {
		
		DownloadStampedDocResponse downloadResponse = downloadStampedDoc(document, audit);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(downloadResponse.getResponseCode())) {
			throw new EmeteraiException(downloadResponse.getMessage(), ReasonEmeterai.DOWNLOAD_DOC_RESPONSE_ERROR);
		}
		String base64Pdf = downloadResponse.getDataReturn().getPdfFile();
		
		DummyClientURLUploadRequest request = new DummyClientURLUploadRequest();
		request.setDocFile(base64Pdf);
		request.setDocNumber(document.getTrDocumentH().getRefNumber());
		request.setTenantCode(document.getMsTenant().getTenantCode());
		
		AuditDataType auditData = new AuditDataType();
		auditData.setCallerId(audit.getCallerId());
		
		request.setAudit(auditData);
		return request;
	}
	
	private UploadStampedDocumentRequest prepareUploadToDmsRequest(TrDocumentD document, String peruriLoginToken, AuditContext audit) {
		
		String documentDate = MssTool.formatDateToStringIn(document.getRequestDate(), "yyyy/MM/dd");
		
		String base64Pdf = getStampedDocument(document, peruriLoginToken, audit);
		
		UploadStampedDocumentRequest request = new UploadStampedDocumentRequest();
		request.setDokumenPeruri(document.getMsPeruriDocType().getDocName());
		request.setDokumenDate(documentDate);
		request.setFilename(document.getDocumentName() + ".pdf");
		request.setContent(base64Pdf);
		request.setNotes(document.getTrDocumentH().getMsLov().getDescription());
		request.setDocumentId(document.getDocumentId());
		
		// Supaya logging tidak besar, buat instance request baru dan ganti base64 string dari PDF ke string dummy
		UploadStampedDocumentRequest loggedRequest = UploadStampedDocumentRequest.newInstance(request);
		loggedRequest.setContent("base64doc");
		String jsonBody = gson.toJson(loggedRequest);
		LOG.info("Kontrak {}, Dokumen {}, Upload document to DMS request body: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonBody);
		
		return request;
	}
	
	private Status uploadManuallyUploadedDocumentToDms(TrDocumentD document, String peruriLoginToken, boolean throwException, AuditContext audit) {
		String jsonResponse = null;
		// Upload document ke client langsung
		if ("1".equals(document.getTrDocumentH().getIsStandardUploadUrl())) {
			return uploadStampedDocToClient(document, jsonResponse, throwException, audit);
		}
		
		// Upload document ke DMS dengan cara CFI
		try {
			String url = document.getTrDocumentH().getUrlUpload();
			if (StringUtils.isEmpty(url)) {
				throw new EmeteraiException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
						new String[] {"Upload URL"}, audit), ReasonEmeterai.UPLOAD_DOC_EXCEPTION);
			}
			
			OkHttpClient okHClient = MssTool.getUnsafeOkHttpClient();
			
			// Prepare header
			Map<String, String> header = new HashMap<>();
			header.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			header.put("Integration", uplDmsIntegrationValue);
			Headers headers = Headers.of(header);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to DMS request header: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), header);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to DMS with URL: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), url);
			
			// Prepare json request body
			UploadStampedDocumentRequest request = prepareUploadToDmsRequest(document, peruriLoginToken, audit);
			String jsonBody = gson.toJson(request);
			RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), jsonBody);
			
			Request okHRequest = new Request.Builder()
					.headers(headers)
					.url(url)
					.post(body).build();
			
			okhttp3.Response okHResponse = okHClient.newCall(okHRequest).execute();
			String result = okHResponse.body().string();
			jsonResponse = result;
			LOG.info("Kontrak {}, Dokumen {}, Upload document to DMS response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), result);
			
			UploadStampedDocumentResponse uploadResponse = gson.fromJson(result, UploadStampedDocumentResponse.class);
			
			if (!"200".equals(uploadResponse.getStatusCode())) {
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.UPL_DMS, StampingErrorDetail.FAIL_RESPONSE, uploadResponse.getMessage(), null, null, jsonResponse, audit);
			}
			
			Status status = new Status();
			status.setCode(Integer.valueOf(uploadResponse.getStatusCode()));
			status.setMessage(uploadResponse.getMessage());
			return status;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Upload document to DMS exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage());
			updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
			checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.UPL_DMS, StampingErrorDetail.EXCEPTION, e.getLocalizedMessage(), e, null, jsonResponse, audit);
			if (throwException) {
				throw new EmeteraiException(e.getLocalizedMessage(), e, ReasonEmeterai.UPLOAD_DOC_EXCEPTION);
			}
			Status status = new Status();
			status.setCode(StatusCode.EMETERAI_UPLOAD_DOC_EXCEPTION);
			status.setMessage(e.getLocalizedMessage());
			return status;
		}
	}
	
	private Status uploadStampedDocToClient(TrDocumentD document, String jsonResponse, boolean throwException, AuditContext audit) {
		try {
			String url = document.getTrDocumentH().getUrlUpload();
			if (StringUtils.isEmpty(url)) {
				throw new EmeteraiException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
						new String[] {"Upload URL"}, audit), ReasonEmeterai.UPLOAD_DOC_EXCEPTION);
			}
			
			OkHttpClient okHClient = MssTool.getUnsafeOkHttpClient();
			String token = daoFactory.getTenantSettingsDao().getTenantSettings(document.getMsTenant(), GlobalVal.CODE_LOV_TENANT_SETTING_TOKEN_CLIENT_URL_UPLOAD).getSettingValue();
			commonValidatorLogic.validateNotNull(token, getMessage("businesslogic.tenantsettings.tenantsettingsnotfoundfortenant", new Object[] {GlobalVal.CODE_LOV_TENANT_SETTING_TOKEN_CLIENT_URL_UPLOAD, document.getMsTenant().getTenantName()}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
			
			
			// Prepare header
			Map<String, String> header = new HashMap<>();
			header.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			header.put(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
			header.put("token", token);
			Headers headers = Headers.of(header);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to Client request header: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), header);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to Client with URL: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), url);
			
			// Prepare json request body
			DummyClientURLUploadRequest request = this.prepareUploadToClientRequest(document, audit);
			String jsonBody = gson.toJson(request);
			
			// Logging json request
			DummyClientURLUploadRequest loggingRequest = DummyClientURLUploadRequest.newInstance(request);
			loggingRequest.setDocFile("base64doc");
			String logJson = gson.toJson(loggingRequest);
			LOG.info("Upload Document to Client Request {}", logJson);
			
			RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), jsonBody);
			
			Request okHRequest = new Request.Builder()
					.headers(headers)
					.url(url)
					.post(body).build();
			
			okhttp3.Response okHResponse = okHClient.newCall(okHRequest).execute();
			String result = okHResponse.body().string();
			jsonResponse = result;
			LOG.info("Kontrak {}, Dokumen {}, Upload document to Client response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), result);
			
			UploadStampedDocumentResponse uploadResponse = gson.fromJson(result, UploadStampedDocumentResponse.class);
			
			if (!"200".equals(uploadResponse.getStatusCode())) {
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.UPL_DMS, StampingErrorDetail.FAIL_RESPONSE, uploadResponse.getMessage(), null, null, jsonResponse, audit);
			}
			
			Status status = new Status();
			status.setCode(Integer.valueOf(uploadResponse.getStatusCode()));
			status.setMessage(uploadResponse.getMessage());
			return status;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Upload document to Client exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage());
			updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
			checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.UPL_DMS, StampingErrorDetail.EXCEPTION, e.getLocalizedMessage(), e, null, jsonResponse, audit);
			if (throwException) {
				throw new EmeteraiException(e.getLocalizedMessage(), e, ReasonEmeterai.UPLOAD_DOC_EXCEPTION);
			}
			Status status = new Status();
			status.setCode(StatusCode.EMETERAI_UPLOAD_DOC_EXCEPTION);
			status.setMessage(e.getLocalizedMessage());
			return status;
		}
	}
	
	private Status uploadDocumentToDms(TrDocumentD document, String peruriLoginToken, boolean throwException, AuditContext audit) {
		try {
			UploadToCoreBean bean = prepareUploadToCoreSingleDocument(document, peruriLoginToken, audit);
			Status status = documentLogic.callUrlUpload(document.getTrDocumentH().getUrlUpload(), bean);
			if (200 != status.getCode()) {
				updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
				checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.UPL_DMS, StampingErrorDetail.FAIL_RESPONSE, status.getMessage(), null, null, null, audit);
			}
			return status;
		} catch (Exception e) {
			updateStatusProcessMeterai(document.getTrDocumentH(), processNumber, null, audit);
			checkAgreementErrorCount(document.getTrDocumentH(), document, StampingErrorLocation.UPL_DMS, StampingErrorDetail.EXCEPTION, e.getLocalizedMessage(), e, null, null, audit);
			if (throwException) {
				throw new EmeteraiException(e.getLocalizedMessage(), e, ReasonEmeterai.UPLOAD_DOC_EXCEPTION);
			}
			Status status = new Status();
			status.setCode(StatusCode.EMETERAI_UPLOAD_DOC_EXCEPTION);
			status.setMessage(e.getLocalizedMessage());
			return status;
		}
	}
	
	private Status uploadStampedDocument(TrDocumentD document, String peruriLoginToken, boolean throwException, AuditContext audit) {
		// Upload DMS dengan cara dokumen ttd WOMF
		if (!"1".equals(document.getTrDocumentH().getIsManualUpload())) {
			return uploadDocumentToDms(document, peruriLoginToken, throwException, audit);
		} 
		
		return uploadManuallyUploadedDocumentToDms(document, peruriLoginToken, throwException, audit);
	}
	
	private UploadToCoreBean prepareUploadToCoreSingleDocument(TrDocumentD document, String peruriLoginToken, AuditContext audit) {
		
		LOG.info("Kontrak {}, Dokumen {}, preparing upload to DMS", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
		
		List<DocumentToUploadBean> documents = new ArrayList<>();
		UploadToCoreBean bean = new UploadToCoreBean();
		DocumentToUploadBean documentBean = new DocumentToUploadBean();
		
		documentBean.setDocTypeTc(document.getMsDocTemplate().getDocTemplateCode());
		documentBean.setDisplayName(document.getMsDocTemplate().getDocTemplateName());
		
		String base64Document = getStampedDocument(document, peruriLoginToken, audit);
		documentBean.setContent(base64Document);
		documentBean.setFileName(GlobalVal.PREFIX_DOCUMENT_FILE_NAME + document.getDocumentId() + ".pdf");
		documents.add(documentBean);
		
		bean.setDocumentObjs(documents);
		bean.setRefNo(document.getTrDocumentH().getRefNumber());
		return bean;
	}
	
	private DownloadStampedDocResponse downloadStampedDocument(String docLink, String token, String refNumber, String documentId,
			long connTimeoutMillis, long readTimeoutMillis) {
		
		try {
			DownloadStampedDocResponse responseResult = new DownloadStampedDocResponse();
			String[] docLinks = docLink.split(DOC_TRX_ID_DELIMITER);
			int lastIndexDocLink = docLinks.length - 1;
			String url = docLinks[lastIndexDocLink];
			
			// Prepare URL and headers
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(headerContentType, headerPdf);
			mapHeader.add(headerAuth, BEARER + token);
			WebClient client = WebClient.create(url).headers(mapHeader);
			MssTool.setWebClientConnReadTimeout(client, connTimeoutMillis, readTimeoutMillis);
			
			// Call API
			Response response = client.get();
			
			// Process the response
			MultivaluedMap<String, Object> responseHeader = response.getHeaders();
			String responseContentType = responseHeader.get(headerContentType).toString();
			responseContentType = responseContentType.substring(1, responseContentType.length() - 1);
			if (headerPdf.equalsIgnoreCase(responseContentType)) {
				InputStream is = (InputStream) response.getEntity();
				byte[] pdfBytes = org.apache.commons.io.IOUtils.toByteArray(is);
				String base64String = Base64.getEncoder().encodeToString(pdfBytes);
				DataReturnBean bean = new DataReturnBean();
				bean.setPdfFile(base64String);
				responseResult.setDataReturn(bean);
				responseResult.setResponseCode(GlobalVal.PERURI_SUCCESS_CODE);
				return responseResult;
			}
			
			// Handling response gagal
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String result = IOUtils.toString(isReader);
			LOG.info("Kontrak {}, Dokumen {}, Download stamped document response: {}", refNumber, documentId, result);
			
			responseResult = gson.fromJson(result, DownloadStampedDocResponse.class);
			responseResult.setResponseCode(PAJAKKU_ERROR_CODE);
			return responseResult;
		} catch (Exception e) {
			throw new EmeteraiException(e.getLocalizedMessage(), e, ReasonEmeterai.DOWNLOAD_DOC_EXCEPTION);
		}
	}
	
	@Override
	public DownloadStampedDocResponse downloadStampedDoc(TrDocumentD documentD, String peruriLoginToken, AuditContext audit) {
		long connTimeoutMillis = getGeneralSettingConnectionTimeouMillisValue(audit);
		long readTimeoutMillis = getGeneralSettingReadTimeouMillisValue(audit);
		
		String refNumber = (null != documentD.getTrDocumentH()) ? documentD.getTrDocumentH().getRefNumber() : null;
		
		return downloadStampedDocument(documentD.getDocumentSdtLink(), peruriLoginToken, refNumber, documentD.getDocumentId(),
				connTimeoutMillis, readTimeoutMillis);
	}

	@Override
	public DownloadStampedDocResponse downloadStampedDoc(TrDocumentD documentD, AuditContext audit) {
		long connTimeoutMillis = getGeneralSettingConnectionTimeouMillisValue(audit);
		long readTimeoutMillis = getGeneralSettingReadTimeouMillisValue(audit);
		
		String refNumber = (null != documentD.getTrDocumentH()) ? documentD.getTrDocumentH().getRefNumber() : null;
		
		EmeteraiPajakkuLoginResponseBean login = loginPajakku(documentD.getTrDocumentH(), true, connTimeoutMillis, readTimeoutMillis, audit);
		return downloadStampedDocument(documentD.getDocumentSdtLink(), login.getToken(), refNumber, documentD.getDocumentId(),
				connTimeoutMillis, readTimeoutMillis);
	}
	
	private void checkAgreementErrorCount(TrDocumentH documentH, TrDocumentD documentD, 
			StampingErrorLocation errorLocation, StampingErrorDetail errorDetail,
			String errorMsg, Exception e, String jsonRequest,
			String jsonResponse, AuditContext audit) {
		
		if (null == documentH) {
			return;
		}
		if (null != e) {
			errorMsg = StringUtils.isBlank(e.getLocalizedMessage()) ? e.toString() : e.getLocalizedMessage();
		}
		
		Long idDocumentH = documentH.getIdDocumentH();
		Long idDocumentD = (null == documentD) ? null : documentD.getIdDocumentD();
		
		String currentErrorLocation = (null != errorLocation) ? errorLocation.toString() : StringUtils.EMPTY;
		String currentErrorLocationDetail = (null != errorDetail) ? errorDetail.toString() : StringUtils.EMPTY;
		String currentErrorMessage = StringUtils.isBlank(errorMsg) ? StringUtils.EMPTY : StringUtils.left(errorMsg, MAX_ERROR_MSG_LENGTH);
		Integer maxErrorCount = getMaxErrorCount(audit);
		
		TrDocumentHStampdutyError documentHSdtError = daoFactory.getDocumentDao().getDocumentHStampdutyErrorNewTran(idDocumentH, idDocumentD);
		
		if (null == documentHSdtError) {
			LOG.info("Kontrak {}, error count = {}, max error count = {}", documentH.getRefNumber(), 0, maxErrorCount);
			documentHSdtError = new TrDocumentHStampdutyError();
			documentHSdtError.setTrDocumentH(documentH);
			documentHSdtError.setTrDocumentD(documentD);
			documentHSdtError.setErrorCount((short) 1);
			documentHSdtError.setErrorLocation(currentErrorLocation);
			documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
			documentHSdtError.setErrorMessage(currentErrorMessage);
			documentHSdtError.setIsEmailSent("0");
			documentHSdtError.setUsrCrt(audit.getCallerId());
			documentHSdtError.setDtmCrt(new Date());
			daoFactory.getDocumentDao().insertDocumentHStampdutyErrorNewTran(documentHSdtError);
			return;
		}
		
		// If repeated error, increment error count
		if (documentHSdtError.getErrorLocation().equals(currentErrorLocation)
				&& documentHSdtError.getErrorLocationDetail().equals(currentErrorLocationDetail)
				&& documentHSdtError.getErrorMessage().equals(currentErrorMessage)) {
			
			short errorCount = documentHSdtError.getErrorCount();
			errorCount += 1;
			
			documentHSdtError.setErrorCount(errorCount);
			documentHSdtError.setDtmUpd(new Date());
			documentHSdtError.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);
				
		} else {
			// Reset error count
			documentHSdtError.setErrorCount(Short.valueOf((short) 1));
			documentHSdtError.setTrDocumentD(documentD);
			documentHSdtError.setErrorLocation(currentErrorLocation);
			documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
			documentHSdtError.setErrorMessage(currentErrorMessage);
			documentHSdtError.setDtmUpd(new Date());
			documentHSdtError.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);
		}
		
		Integer errorCount = Integer.valueOf(documentHSdtError.getErrorCount());
		LOG.info("Kontrak {}, error count = {}, max error count = {}", documentH.getRefNumber(), errorCount, maxErrorCount);
		if (errorCount >= maxErrorCount) {
			documentHSdtError.setIsEmailSent("1");
			documentHSdtError.setUsrUpd(audit.getCallerId());
			documentHSdtError.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);
			
			sendAttachEmeteraiErrorEmail(documentHSdtError, e, jsonRequest, jsonResponse, audit);
			updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_FAILED, null, audit);
		}
	}
	
	private void sendAttachEmeteraiErrorEmail(TrDocumentHStampdutyError documentHSdtError, Exception e, String jsonRequest, String jsonResponse, AuditContext audit) {
		if (null == documentHSdtError) {
			return;
		}
		
		EmailAttachmentBean[] attachments = null;
		if (null != e) {
			byte[] stackTraceFile = buildStackTraceTextFile(e);
			String filename = buildStackTraceFileName(documentHSdtError.getErrorLocation(), documentHSdtError.getErrorLocationDetail());
			EmailAttachmentBean attachment = new EmailAttachmentBean(stackTraceFile, filename);
			attachments = new EmailAttachmentBean[] {attachment};
		}
		
		TrDocumentH documentH = documentHSdtError.getTrDocumentH();
		TrDocumentD documentD = documentHSdtError.getTrDocumentD();
		String totalMeterai = (null == documentD) ? MSG_EMPTY_DOCUMENT : String.valueOf(documentD.getTotalMaterai());
		String totalStamping = (null == documentD) ? MSG_EMPTY_DOCUMENT : String.valueOf(documentD.getTotalStamping());
		String documentId = (null == documentD) ? MSG_EMPTY_DOCUMENT : documentD.getDocumentId();
		
		Map<String, Object> kontrak = new HashMap<>();
		kontrak.put("refNumber", documentH.getRefNumber());
		kontrak.put("documentId", documentId);
		kontrak.put("totalMeterai", totalStamping + "/" + totalMeterai);
		kontrak.put("tenantName", documentH.getMsTenant().getTenantName());
		
		String errorLocation = StringUtils.isBlank(documentHSdtError.getErrorLocation()) ? "(error location unspecified)" : documentHSdtError.getErrorLocation();
		String errorLocationDetail = StringUtils.isBlank(documentHSdtError.getErrorLocationDetail()) ? "(error location detail unspecified)" : documentHSdtError.getErrorLocationDetail();
		String errorMsg = StringUtils.isBlank(documentHSdtError.getErrorMessage()) ? "(error message unspecified)" : documentHSdtError.getErrorMessage();
		String request = StringUtils.isBlank(jsonRequest) ? "(no request)" : jsonRequest;
		String response = StringUtils.isBlank(jsonResponse) ? "(no response)" : jsonResponse;
		
		Map<String, Object> error = new HashMap<>();
		error.put("errorTime", MssTool.formatDateToStringIn(documentHSdtError.getDtmUpd(), GlobalVal.DATE_TIME_FORMAT_SEC));
		error.put("errorCount", documentHSdtError.getErrorCount());
		error.put("errorLocation", errorLocation);
		error.put("errorLocationDetail", errorLocationDetail);
		error.put("errorMsg", errorMsg);
		error.put("request", request);
		error.put("response", response);
		
		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("kontrak", kontrak);
		templateParameters.put("error", error);
		
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_ATTACH_EMETERAI_ERROR, templateParameters);
		
		String[] receivers = getErrorEmailRecipients(documentH.getMsTenant());
		
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setFrom(fromEmailAddr);
		emailInfo.setSubject(template.getSubject());
		emailInfo.setBodyMessage(template.getBody());
		emailInfo.setTo(receivers);
		try {
			emailSenderLogic.sendEmail(emailInfo, attachments);
		} catch (Exception e1) {
			LOG.error("Error during sending attach emeterai error email: {}", e1.getLocalizedMessage());
		}
	}
	
	private byte[] buildStackTraceTextFile(Exception e) {
		String stackTrace = ExceptionUtils.getStackTrace(e);
		String base64 = Base64.getEncoder().encodeToString(stackTrace.getBytes());
		return Base64.getDecoder().decode(base64);
	}
	
	private String buildStackTraceFileName(String errorLocation, String errorLocationDetail) {
		String currentTime = MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_TIME_FORMAT_SEQ);
		StringBuilder filename = new StringBuilder()
				.append(StringUtils.upperCase(errorLocation)).append("_")
				.append(StringUtils.upperCase(errorLocationDetail)).append("_")
				.append(currentTime)
				.append(".txt");
		return filename.toString();
	}
	
	private ViewDocumentResponse getDocumentFileFromVendor(TrDocumentD document, boolean throwException, AuditContext audit) {
		ViewDocumentRequest request = new ViewDocumentRequest();
		request.setDocumentId(document.getDocumentId());
		
		try {
			return documentLogic.viewDocumentWithoutSecurity(request, audit);
		} catch (Exception e) {
			if (throwException) {
				throw new EmeteraiException(e.getLocalizedMessage(), e, ReasonEmeterai.DOWNLOAD_DOC_EXCEPTION);
			}
			Status status = new Status();
			status.setCode(StatusCode.DOCUMENT_FILE_INACCESSIBLE);
			status.setMessage(e.getLocalizedMessage());
			
			ViewDocumentResponse response = new ViewDocumentResponse();
			response.setStatus(status);
			return response;
		}
	}
	
	private String getDocumentFileToUpload(TrDocumentD document, String peruriLoginToken, AuditContext audit) {
		String transactionId = document.getTransactionId();
		String[] transactionIds = {};
		
		if (StringUtils.isNotBlank(transactionId)) {
			transactionIds = transactionId.split(";");
		}
		
		// Download stamped document from OSS
		if (transactionIds.length > 0) {
			return getStampedDocument(document, peruriLoginToken, audit);
		}
		
		// Download manually uploaded document from OSS
		if ("1".equals(document.getTrDocumentH().getIsManualUpload())) {
			return getManuallyUploadedDocumentFromOss(document, audit);
		}
		
		// Download from document vendor
		ViewDocumentResponse viewResponse = getDocumentFileFromVendor(document, true, audit);
		if (null != viewResponse.getStatus() && 0 != viewResponse.getStatus().getCode()) {
			throw new EmeteraiException(viewResponse.getStatus().getMessage(), ReasonEmeterai.DOWNLOAD_DOC_RESPONSE_ERROR);
		}
		return viewResponse.getPdfBase64();
	}

	@Override
	public PajakkuDocumentTypeBean getDocumentType() throws IOException {
		
		EmeteraiPajakkuLoginResponseBean token = this.getToken();
		
		WebClient clientDocumentType = WebClient.create(urlPajakkuDocumentType).authorization(token.getToken());

        Response responseDocumentType = clientDocumentType.get();

        InputStreamReader isReaderDocumentType = new InputStreamReader((InputStream) responseDocumentType.getEntity());

        String resultDocumentType = IOUtils.toString(isReaderDocumentType);
        LOG.info("JSON document type result : {}", resultDocumentType);
        return gson.fromJson(resultDocumentType, PajakkuDocumentTypeBean.class);
	}

	@Override
	public EmeteraiPajakkuLoginResponseBean getToken() throws IOException {
		EmeteraiPajakkuLoginResponseBean response;
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerJson);
		WebClient client = WebClient.create(urlLogin).headers(mapHeader);
		
		EmeteraiPajakkuLoginRequestBean request = new EmeteraiPajakkuLoginRequestBean();
		request.setUser(userPajakku);
		request.setPassword(passwordPajakku);
		
		String jsonRequest = gson.toJson(request);
		LOG.info(" JSON request login Pajakku: {}", jsonRequest);
		
		Response clientResponse = client.post(jsonRequest);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) clientResponse.getEntity());
		String result = IOUtils.toString(isReader);
		LOG.info(" JSON response login Pajakku: {}", result);
		
		response = gson.fromJson(result, EmeteraiPajakkuLoginResponseBean.class);
		response.setJsonRequest(jsonRequest);
		response.setJsonResponse(result);
		return response;
	}
	
}
